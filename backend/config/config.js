require('dotenv').config();

// Environment-specific configurations
const environments = {
  local: {
    mongo: {
      connectionString: process.env.MONGODB_QA_URI,
    }
  },
  prod: {
    mongo: {
      connectionString: process.env.MONGODB_PROD_URI
    }
  }
};

// Get current environment
const currentEnv = process.env.NODE_ENV || 'prod';

// Get environment-specific config
const envConfig = environments[currentEnv] || environments.prod;

module.exports = {
  server: {
    port: process.env.PORT || 80,
    nodeEnv: currentEnv,
    corsOrigin: ['https://kafkadashboard.policybazaar.com', 'http://localhost:3000']
  },
  rateLimiting: {
    windowMs: 900000, // 15 minutes
    max: 100
  },
  logging: {
    level: 'info'
  },
  mongodb: {
    uri: envConfig.mongo.connectionString,
    options: {
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
    }
  },
  jwt: {
    secret: process.env.JWT_SECRET,
    expiresIn: process.env.JWT_EXPIRES_IN || '24h'
  },
  environment: currentEnv
}; 