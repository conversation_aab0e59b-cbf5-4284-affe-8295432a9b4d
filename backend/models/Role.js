const mongoose = require('mongoose');

// Define available permissions
const PERMISSIONS = {
  // Dashboard permissions
  VIEW_DASHBOARD: 'view_dashboard',
  
  // Cluster permissions
  VIEW_CLUSTER_INFO: 'view_cluster_info',
  
  // User management permissions
  MANAGE_USERS: 'manage_users',
  VIEW_USERS: 'view_users',
  CREATE_USERS: 'create_users',
  UPDATE_USERS: 'update_users',
  DELETE_USERS: 'delete_users',
  
  // Topic permissions
  VIEW_ALL_TOPICS: 'view_all_topics',
  VIEW_ASSIGNED_TOPICS: 'view_assigned_topics',
  CREATE_TOPICS: 'create_topics',
  UPDATE_TOPICS: 'update_topics',
  DELETE_TOPICS: 'delete_topics',
  MANAGE_TOPIC_PARTITIONS: 'manage_topic_partitions',
  
  // Message permissions
  VIEW_MESSAGES: 'view_messages',
  PRODUCE_MESSAGES: 'produce_messages',
  
  // Consumer group permissions
  VIEW_ALL_CONSUMER_GROUPS: 'view_all_consumer_groups',
  VIEW_ASSIGNED_CONSUMER_GROUPS: 'view_assigned_consumer_groups',
  DELETE_CONSUMER_GROUPS: 'delete_consumer_groups',
  
  // Environment permissions
  SWITCH_ENVIRONMENT: 'switch_environment'
};

const roleSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    unique: true,
    enum: ['SUPER_ADMIN', 'TOPIC_MANAGER', 'TOPIC_VIEWER']
  },
  displayName: {
    type: String,
    required: true
  },
  description: {
    type: String,
    required: true
  },
  permissions: [{
    type: String,
    enum: Object.values(PERMISSIONS)
  }],
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true,
  collection: 'kafkadashboard-roles' // Specific collection name
});

// Define default roles and their permissions
const DEFAULT_ROLES = {
  SUPER_ADMIN: {
    displayName: 'Super Administrator',
    description: 'Full access to all features and functionality',
    permissions: Object.values(PERMISSIONS)
  },
  TOPIC_MANAGER: {
    displayName: 'Topic Manager',
    description: 'Can manage assigned topics, view messages, and produce messages',
    permissions: [
      PERMISSIONS.VIEW_ASSIGNED_TOPICS,
      PERMISSIONS.CREATE_TOPICS,
      PERMISSIONS.UPDATE_TOPICS,
      PERMISSIONS.DELETE_TOPICS,
      PERMISSIONS.MANAGE_TOPIC_PARTITIONS,
      PERMISSIONS.VIEW_MESSAGES,
      PERMISSIONS.PRODUCE_MESSAGES,
      PERMISSIONS.VIEW_ASSIGNED_CONSUMER_GROUPS,
      PERMISSIONS.DELETE_CONSUMER_GROUPS,
      PERMISSIONS.SWITCH_ENVIRONMENT
    ]
  },
  TOPIC_VIEWER: {
    displayName: 'Topic Viewer',
    description: 'Read-only access to assigned topics and messages',
    permissions: [
      PERMISSIONS.VIEW_ASSIGNED_TOPICS,
      PERMISSIONS.VIEW_MESSAGES,
      PERMISSIONS.VIEW_ASSIGNED_CONSUMER_GROUPS,
      PERMISSIONS.SWITCH_ENVIRONMENT
    ]
  }
};

// Static method to initialize default roles
roleSchema.statics.initializeDefaultRoles = async function() {
  try {
    for (const [roleName, roleData] of Object.entries(DEFAULT_ROLES)) {
      await this.findOneAndUpdate(
        { name: roleName },
        {
          name: roleName,
          displayName: roleData.displayName,
          description: roleData.description,
          permissions: roleData.permissions,
          isActive: true
        },
        { upsert: true, new: true }
      );
    }
  } catch (error) {
    console.error('Error initializing default roles:', error);
  }
};

// Method to check if role has specific permission
roleSchema.methods.hasPermission = function(permission) {
  return this.permissions.includes(permission);
};

module.exports = {
  Role: mongoose.model('Role', roleSchema),
  PERMISSIONS,
  DEFAULT_ROLES
};
