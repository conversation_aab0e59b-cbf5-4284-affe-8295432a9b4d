const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
  username: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    minlength: 3,
    maxlength: 30
  },
  email: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    lowercase: true,
    validate: {
      validator: function(email) {
        const emailRegex = /^[a-zA-Z0-9._%+-]+@policybazaar\.com$/;
        return emailRegex.test(email);
      },
      message: 'Email must be from @policybazaar.com domain'
    }
  },
  password: {
    type: String,
    required: true,
    minlength: 6
  },
  role: {
    type: String,
    enum: ['SUPER_ADMIN', 'TOPIC_MANAGER', 'TOPIC_VIEWER'],
    default: 'TOPIC_VIEWER'
  },
  // For TOPIC_MANAGER and TOPIC_VIEWER roles - topics they have access to
  assignedTopics: [{
    type: String,
    trim: true
  }],
  // Flag to indicate if user has access to all topics (useful for some TOPIC_MANAGER users)
  hasAllTopicsAccess: {
    type: <PERSON><PERSON>an,
    default: false
  },
  isActive: {
    type: Boolean,
    default: true
  },
  lastLogin: {
    type: Date
  },
  loginAttempts: {
    type: Number,
    default: 0
  },
  lockUntil: {
    type: Date
  },
  // New field to track if user needs to change password on first login
  requiresPasswordChange: {
    type: Boolean,
    default: false
  },
  // Track when password was last changed
  passwordChangedAt: {
    type: Date
  },
  // Track if password was reset by admin (for first-time users)
  passwordResetByAdmin: {
    type: Boolean,
    default: false
  }
}, {
  timestamps: true,
  collection: 'kafkadashboard-users' // Specific collection name
});

// Virtual for account lock status
userSchema.virtual('isLocked').get(function() {
  return !!(this.lockUntil && this.lockUntil > Date.now());
});

// Hash password before saving
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  
  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    this.passwordChangedAt = new Date();
    next();
  } catch (error) {
    next(error);
  }
});

// Compare password method
userSchema.methods.comparePassword = async function(candidatePassword) {
  if (this.isLocked) {
    throw new Error('Account is locked due to too many failed login attempts');
  }
  
  const isMatch = await bcrypt.compare(candidatePassword, this.password);
  
  if (!isMatch) {
    // Increment login attempts
    this.loginAttempts += 1;
    
    // Lock account after 5 failed attempts for 30 minutes
    if (this.loginAttempts >= 5) {
      this.lockUntil = Date.now() + (30 * 60 * 1000); // 30 minutes
    }
    
    await this.save();
    throw new Error('Invalid credentials');
  }
  
  // Reset login attempts on successful login
  if (this.loginAttempts > 0) {
    this.loginAttempts = 0;
    this.lockUntil = undefined;
  }
  
  this.lastLogin = new Date();
  await this.save();
  
  return true;
};

// Method to change password
userSchema.methods.changePassword = async function(newPassword) {
  if (newPassword.length < 6) {
    throw new Error('Password must be at least 6 characters long');
  }
  
  this.password = newPassword;
  this.requiresPasswordChange = false;
  this.passwordResetByAdmin = false; // Reset the admin reset flag
  await this.save();
  
  return true;
};

// Method to reset password (for admin use)
userSchema.methods.resetPassword = async function(newPassword) {
  if (newPassword.length < 6) {
    throw new Error('Password must be at least 6 characters long');
  }
  
  this.password = newPassword;
  this.requiresPasswordChange = false; // Allow user to login with reset password first
  this.passwordResetByAdmin = true; // Mark that password was reset by admin
  this.loginAttempts = 0;
  this.lockUntil = undefined;
  await this.save();
  
  return true;
};

// Remove sensitive data when converting to JSON
userSchema.methods.toJSON = function() {
  const user = this.toObject();
  delete user.password;
  delete user.loginAttempts;
  delete user.lockUntil;
  return user;
};

module.exports = mongoose.model('User', userSchema); 