const crypto = require('crypto');

/**
 * Generate a secure random password
 * @param {number} length - Length of password (default: 12)
 * @returns {string} - Generated password
 */
function generateSecurePassword(length = 12) {
  // Define character sets - avoid confusing characters
  const lowercase = 'abcdefghijklmnopqrstuvwxyz';
  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const numbers = '0123456789';
  const symbols = '!@#$%^&*'; // Only safe symbols
  
  // Ensure at least one character from each set
  let password = '';
  password += lowercase[crypto.randomInt(lowercase.length)];
  password += uppercase[crypto.randomInt(uppercase.length)];
  password += numbers[crypto.randomInt(numbers.length)];
  password += symbols[crypto.randomInt(symbols.length)];
  
  // Fill the rest with random characters from all sets
  const allChars = lowercase + uppercase + numbers + symbols;
  for (let i = 4; i < length; i++) {
    password += allChars[crypto.randomInt(allChars.length)];
  }
  
  // Shuffle the password to make it more random
  return password.split('').sort(() => Math.random() - 0.5).join('');
}

/**
 * Extract username from email
 * @param {string} email - Email address
 * @returns {string} - Username (part before @)
 */
function extractUsernameFromEmail(email) {
  return email.split('@')[0];
}

/**
 * Validate email domain
 * @param {string} email - Email address
 * @returns {boolean} - True if valid policybazaar.com email
 */
function validatePolicyBazaarEmail(email) {
  const emailRegex = /^[a-zA-Z0-9._%+-]+@policybazaar\.com$/;
  return emailRegex.test(email);
}

module.exports = {
  generateSecurePassword,
  extractUsernameFromEmail,
  validatePolicyBazaarEmail
}; 