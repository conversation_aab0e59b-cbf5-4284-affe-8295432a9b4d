const logger = require('./logger');

// In-memory storage for daily message counts (in production, use Redis or database)
const dailyMessageCounts = new Map();

/**
 * Get the current date key for tracking
 */
const getDateKey = () => {
  const now = new Date();
  return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`;
};

/**
 * Get user's daily message count
 */
const getUserDailyMessageCount = (userId) => {
  const dateKey = getDateKey();
  const userKey = `${userId}-${dateKey}`;
  return dailyMessageCounts.get(userKey) || 0;
};

/**
 * Increment user's daily message count
 */
const incrementUserDailyMessageCount = (userId, count = 1) => {
  const dateKey = getDateKey();
  const userKey = `${userId}-${dateKey}`;
  const currentCount = dailyMessageCounts.get(userKey) || 0;
  const newCount = currentCount + count;
  dailyMessageCounts.set(userKey, newCount);
  
  logger.info(`User ${userId} message count: ${currentCount} -> ${newCount} (daily limit: 1000)`);
  logger.debug(`Daily message counts map size: ${dailyMessageCounts.size}`);
  logger.debug(`All user keys: ${Array.from(dailyMessageCounts.keys()).join(', ')}`);
  
  return newCount;
};

/**
 * Check if user has exceeded daily message limit
 */
const hasExceededDailyLimit = (userId) => {
  const currentCount = getUserDailyMessageCount(userId);
  return currentCount >= 1000;
};

/**
 * Get remaining daily message count
 */
const getRemainingDailyCount = (userId) => {
  const currentCount = getUserDailyMessageCount(userId);
  return Math.max(0, 1000 - currentCount);
};

/**
 * Clean up old daily counts (keep only last 7 days)
 */
const cleanupOldCounts = () => {
  const today = new Date();
  const keysToDelete = [];
  
  for (const [key] of dailyMessageCounts) {
    const parts = key.split('-');
    if (parts.length >= 4) {
      const dateStr = `${parts[1]}-${parts[2]}-${parts[3]}`;
      const keyDate = new Date(dateStr);
      const daysDiff = (today - keyDate) / (1000 * 60 * 60 * 24);
      
      if (daysDiff > 7) {
        keysToDelete.push(key);
      }
    }
  }
  
  keysToDelete.forEach(key => dailyMessageCounts.delete(key));
  
  if (keysToDelete.length > 0) {
    logger.info(`Cleaned up ${keysToDelete.length} old message count entries`);
  }
};

// Clean up old counts every hour
setInterval(cleanupOldCounts, 60 * 60 * 1000);

module.exports = {
  getUserDailyMessageCount,
  incrementUserDailyMessageCount,
  hasExceededDailyLimit,
  getRemainingDailyCount,
  cleanupOldCounts
}; 