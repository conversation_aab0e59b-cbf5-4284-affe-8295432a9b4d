const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const http = require('http');
const socketIo = require('socket.io');
const mongoose = require('mongoose');
const config = require('./config/config');
const logger = require('./utils/logger');
const kafkaClient = require('./kafka/dynamicKafkaClient');
const { rbacService } = require('./services/rbacService');

// Import routes
const authRoutes = require('./routes/auth');
const topicsRoutes = require('./routes/topics');
const consumersRoutes = require('./routes/consumers');
const clusterRoutes = require('./routes/cluster');
const environmentRoutes = require('./routes/environment');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: config.server.corsOrigin,
    methods: ['GET', 'POST']
  }
});

// Middleware
app.use(helmet());
app.use(compression());
app.use(cors({
  origin: config.server.corsOrigin,
  credentials: true
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Rate limiting
const limiter = rateLimit({
  windowMs: config.rateLimiting.windowMs,
  max: config.rateLimiting.max,
  message: 'Too many requests from this IP, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
});
app.use('/api/', limiter);

// Request logging - only log in development
if (process.env.NODE_ENV !== 'production') {
  app.use((req, res, next) => {
    logger.info(`${req.method} ${req.path} - ${req.ip}`);
    next();
  });
}

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/topics', topicsRoutes);
app.use('/api/consumers', consumersRoutes);
app.use('/api/cluster', clusterRoutes);
app.use('/api/environment', environmentRoutes);

// Health check endpoint (requires authentication for security)
app.get('/api/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    kafka: kafkaClient.isHealthy() ? 'connected' : 'disconnected'
  });
});

// Public health check endpoint for AWS Load Balancer (no authentication required)
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: Math.floor(process.uptime())
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  logger.error(`Error: ${err.message}`, {
    stack: err.stack,
    url: req.url,
    method: req.method,
    ip: req.ip
  });
  
  // Don't expose internal errors to client
  const errorMessage = process.env.NODE_ENV === 'production' 
    ? 'Internal server error' 
    : err.message;
    
  res.status(err.status || 500).json({
    error: {
      message: errorMessage,
      status: err.status || 500
    }
  });
});

// Global error handlers
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', {
    error: error.message,
    stack: error.stack
  });
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection:', {
    reason: reason,
    promise: promise
  });
});

// Graceful shutdown
const gracefulShutdown = async (signal) => {
  logger.info(`${signal} received, shutting down gracefully`);
  
  try {
    // Cleanup Kafka clients
    await kafkaClient.cleanup();
    logger.info('Kafka cleanup completed');
    
    // Close server
    server.close((err) => {
      if (err) {
        logger.error('Error closing server:', err);
        process.exit(1);
      } else {
        logger.info('Server closed successfully');
        process.exit(0);
      }
    });
  } catch (error) {
    logger.error('Error during graceful shutdown:', error);
    process.exit(1);
  }
};

// Signal handlers
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Socket.IO connection handling
io.on('connection', (socket) => {
  logger.debug(`Client connected: ${socket.id}`);

  // Handle topic subscription
  socket.on('subscribe-topic', (topicName) => {
    logger.debug(`Client ${socket.id} subscribing to topic: ${topicName}`);
    socket.join(`topic-${topicName}`);
    socket.emit('subscription-confirmed', { topic: topicName });
  });

  // Handle topic unsubscription
  socket.on('unsubscribe-topic', (topicName) => {
    logger.debug(`Client ${socket.id} unsubscribing from topic: ${topicName}`);
    socket.leave(`topic-${topicName}`);
    socket.emit('unsubscription-confirmed', { topic: topicName });
  });

  // Handle client disconnect
  socket.on('disconnect', () => {
    logger.debug(`Client disconnected: ${socket.id}`);
  });

  // Handle connection errors
  socket.on('error', (error) => {
    logger.error(`Socket error for client ${socket.id}:`, error);
  });
});

// Make io available globally for routes
global.io = io;

// Initialize Kafka client and start server
async function startServer() {
  try {
    logger.info('Starting Kafka Dashboard Backend...');
    
    // Connect to MongoDB
    try {
      await mongoose.connect(config.mongodb.uri, config.mongodb.options);
      logger.info('MongoDB connected successfully');

      // Initialize RBAC system
      await rbacService.initialize();
      logger.info('RBAC system initialized');
    } catch (error) {
      logger.error('Failed to connect to MongoDB:', error);
      logger.info('Server will continue running without authentication features.');
    }

    // Start the HTTP server
    server.listen(config.server.port, () => {
      logger.info(`🚀 Kafka Dashboard Backend Started on port ${config.server.port}`);
      logger.info(`Environment: ${config.environment.toUpperCase()}`);
      logger.info(`MongoDB: Connected`);
      logger.info(`Kafka: ${kafkaClient.isHealthy() ? 'Connected' : 'Disconnected'}`);
    });

    // Try to connect to Kafka (non-blocking)
    try {
      await kafkaClient.connect();
      logger.info('Kafka client connected successfully');
    } catch (error) {
      logger.error('Failed to connect to Kafka at startup:', error);
      logger.info('Server will continue running. Kafka connection will be retried when needed.');
    }
    
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Export io for use in other modules
module.exports = { io };

startServer(); 