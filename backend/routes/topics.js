const express = require('express');
const router = express.Router();
const kafkaClient = require('../kafka/dynamicKafkaClient');
const logger = require('../utils/logger');
const rateLimit = require('express-rate-limit');
const { body, validationResult } = require('express-validator');
const { authenticateToken } = require('../middleware/auth');
const {
  requirePermission,
  requireTopicAccess,
  filterTopicsByAccess,
  requireTopicManagement,
  PERMISSIONS
} = require('../middleware/rbac');
const { rbacService } = require('../services/rbacService');
const { checkAndIncrementSingleMessage, checkAndIncrementBulkMessages } = require('../middleware/messageLimit');

// Rate limiting for topic operations
const topicRateLimit = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 10, // limit each IP to 10 requests per windowMs
  message: { error: 'Too many topic operations, please try again later' }
});

// Message rate limiting
const messageRateLimit = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 100, // limit each IP to 100 message requests per windowMs
  message: { error: 'Too many message requests, please try again later' }
});

// GET /api/topics - Get topics based on user access
router.get('/', authenticateToken, filterTopicsByAccess, async (req, res) => {
  try {
    const { includeCounts } = req.query;

    let allTopics;
    if (includeCounts === 'true') {
      allTopics = await kafkaClient.getTopicsWithMessageCounts();
    } else {
      allTopics = await kafkaClient.getTopics();
    }

    // Filter topics based on user access
    const accessibleTopics = await rbacService.getUserAccessibleTopics(req.user.id, allTopics);

    res.json({
      success: true,
      data: accessibleTopics
    });
  } catch (error) {
    logger.error('Error fetching topics:', error);
    res.status(500).json({
      error: {
        message: 'Failed to fetch topics',
        status: 500
      }
    });
  }
});

// GET /api/topics/:topicName - Get topic details
router.get('/:topicName', authenticateToken, requireTopicAccess('topicName'), async (req, res) => {
  try {
    const { topicName } = req.params;
    const topics = await kafkaClient.getTopics();
    const topic = topics.find(t => t.name === topicName);
    
    if (!topic) {
      return res.status(404).json({
        error: {
          message: 'Topic not found',
          status: 404
        }
      });
    }
    
    res.json({
      success: true,
      data: topic
    });
  } catch (error) {
    logger.error('Error fetching topic:', error);
    res.status(500).json({
      error: {
        message: 'Failed to fetch topic',
        status: 500
      }
    });
  }
});

// POST /api/topics - Create a new topic (REMOVED FOR VAPT COMPLIANCE)
// DELETE /api/topics/:topicName - Delete a topic (REMOVED FOR VAPT COMPLIANCE)

// GET /api/topics/:topicName/messages - Get messages from a topic (requires topic access)
router.get('/:topicName/messages', authenticateToken, requireTopicAccess('topicName'), async (req, res) => {
  try {
    const { topicName } = req.params;
    const { partition = -1, offset = 0, limit = 100, startFrom = 'latest' } = req.query;
    
    // Validate parameters
    const parsedLimit = Math.min(parseInt(limit) || 100, 1000); // Max 1000 messages
    const parsedPartition = parseInt(partition) || -1;
    
    logger.debug(`Fetching messages from topic: ${topicName}, partition: ${parsedPartition}, limit: ${parsedLimit}, startFrom: ${startFrom}`);
    
    let messages;

    // Use different methods based on the startFrom parameter
    if (startFrom === 'latest') {
      // Use the simpler, more reliable method for latest messages
      messages = await kafkaClient.getRecentMessages(topicName, parsedPartition, parsedLimit);
    } else if (startFrom === 'earliest') {
      messages = await kafkaClient.getMessagesFromOffset(topicName, parsedPartition, startFrom, parsedLimit);
    } else {
      messages = await kafkaClient.getMessages(topicName, parsedPartition, parseInt(offset) || 0, parsedLimit);
    }
    
    // Add metadata about the request
    const response = {
      success: true,
      data: messages,
      metadata: {
        topicName,
        partition: parsedPartition,
        limit: parsedLimit,
        startFrom,
        messageCount: messages.length,
        timestamp: new Date().toISOString()
      }
    };
    
    res.json(response);
  } catch (error) {
    logger.error(`Error fetching messages for topic ${req.params.topicName}:`, error);
    res.status(500).json({
      success: false,
      error: error.message,
      data: [] // Return empty array instead of failing completely
    });
  }
});

// POST /api/topics/:topicName/messages - Produce a message to a topic (requires topic access and produce permission)
router.post('/:topicName/messages', authenticateToken, requireTopicAccess('topicName'), requirePermission(PERMISSIONS.PRODUCE_MESSAGES), messageRateLimit, checkAndIncrementSingleMessage, [
  body('value').isString().withMessage('Message value is required'),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: {
          message: 'Validation failed',
          details: errors.array(),
          status: 400
        }
      });
    }

    const { topicName } = req.params;
    const result = await kafkaClient.produceMessage(topicName, req.body);
    
    // Get remaining count after message production
    const { getRemainingDailyCount } = require('../utils/messageTracker');
    const remainingCount = getRemainingDailyCount(req.user.id);
    
    res.json({
      ...result,
      remainingMessages: remainingCount
    });
  } catch (error) {
    logger.error('Error producing message:', error);
    res.status(500).json({
      error: {
        message: 'Failed to produce message',
        status: 500
      }
    });
  }
});

// POST /api/topics/:topicName/messages/bulk - Produce multiple messages to a topic (requires topic access and produce permission)
router.post('/:topicName/messages/bulk', authenticateToken, requireTopicAccess('topicName'), requirePermission(PERMISSIONS.PRODUCE_MESSAGES), messageRateLimit, checkAndIncrementBulkMessages, [
  body('messages').isArray({ min: 1, max: 1000 }).withMessage('Messages must be an array with 1-1000 items'),
  body('messages.*.value').isString().withMessage('Message value is required'),
  body('messages.*.key').optional().isString().withMessage('Message key must be a string'),
  body('messages.*.partition').optional().isInt({ min: 0 }).withMessage('Partition must be a non-negative integer'),
  body('messages.*.headers').optional().isObject().withMessage('Headers must be an object'),
  body('batchSize').optional().isInt({ min: 1, max: 100 }).withMessage('Batch size must be between 1 and 100'),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: {
          message: 'Validation failed',
          details: errors.array(),
          status: 400
        }
      });
    }

    const { topicName } = req.params;
    const { messages, batchSize = 10 } = req.body;
    
    logger.info(`Bulk producing ${messages.length} messages to topic: ${topicName}, batch size: ${batchSize}`);
    
    const result = await kafkaClient.produceMessagesBulk(topicName, messages, batchSize);
    
    // Get remaining count after message production
    const { getRemainingDailyCount } = require('../utils/messageTracker');
    const remainingCount = getRemainingDailyCount(req.user.id);
    
    res.json({
      ...result,
      remainingMessages: remainingCount
    });
  } catch (error) {
    logger.error('Error producing bulk messages:', error);
    res.status(500).json({
      error: {
        message: 'Failed to produce bulk messages',
        status: 500
      }
    });
  }
});

// POST /api/topics/:topicName/partitions - Add partitions to a topic (REMOVED FOR VAPT COMPLIANCE)

// GET /api/topics/:topicName/config - Get topic configuration
router.get('/:topicName/config', authenticateToken, requireTopicAccess('topicName'), async (req, res) => {
  try {
    const { topicName } = req.params;
    const config = await kafkaClient.getTopicConfig(topicName);
    res.json({
      success: true,
      data: config
    });
  } catch (error) {
    logger.error('Error fetching topic config:', error);
    res.status(500).json({
      error: {
        message: 'Failed to fetch topic configuration',
        status: 500
      }
    });
  }
});

// POST /api/topics/:topicName/subscribe - Subscribe to real-time messages
router.post('/:topicName/subscribe', authenticateToken, requireTopicAccess('topicName'), async (req, res) => {
  try {
    const { topicName } = req.params;
    logger.info(`Starting real-time subscription for topic: ${topicName}, user: ${req.user.username}`);
    
    // Get global io instance
    const io = global.io;
    
    if (!io) {
      logger.error('Socket.IO not available for topic subscription');
      return res.status(500).json({
        error: {
          message: 'Socket.IO not available',
          status: 500
        }
      });
    }
    
    // Start real-time consumer
    const result = await kafkaClient.startRealtimeConsumer(topicName, (messageData) => {
      logger.debug(`Emitting real-time message for topic ${topicName}: ${messageData.offset}`);
      // Emit message to all clients subscribed to this topic
      io.to(`topic-${topicName}`).emit('message', messageData);
    });
    
    logger.info(`Real-time subscription started for topic: ${topicName}`);
    res.json(result);
  } catch (error) {
    logger.error('Error subscribing to topic:', error);
    res.status(500).json({
      error: {
        message: 'Failed to subscribe to topic',  
        status: 500
      }
    });
  }
});

// POST /api/topics/:topicName/unsubscribe - Unsubscribe from real-time messages
router.post('/:topicName/unsubscribe', authenticateToken, requireTopicAccess('topicName'), async (req, res) => {
  try {
    const { topicName } = req.params;
    logger.info(`Stopping real-time subscription for topic: ${topicName}, user: ${req.user.username}`);
    
    const result = await kafkaClient.stopRealtimeConsumer(topicName);
    
    logger.info(`Real-time subscription stopped for topic: ${topicName}`);
    res.json(result);
  } catch (error) {
    logger.error('Error unsubscribing from topic:', error);
    res.status(500).json({
      error: {
        message: 'Failed to unsubscribe from topic',
        status: 500
      }
    });
  }
});

// GET /api/topics/:topicName/message-count - Get message count for a specific topic
router.get('/:topicName/message-count', authenticateToken, requireTopicAccess('topicName'), async (req, res) => {
  try {
    const { topicName } = req.params;
    const messageCount = await kafkaClient.getTopicMessageCount(topicName);
    
    res.json({
      success: true,
      data: messageCount
    });
  } catch (error) {
    logger.error(`Error fetching message count for topic ${req.params.topicName}:`, error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// GET /api/topics/:topicName/search - Search messages with filters (requires topic access)
router.get('/:topicName/search', authenticateToken, requireTopicAccess('topicName'), messageRateLimit, async (req, res) => {
  try {
    const { topicName } = req.params;
    const { 
      partition = -1, 
      limit = 100, 
      startFrom = 'latest',
      key = '',
      value = '',
      startTimestamp = '',
      endTimestamp = '',
      caseSensitive = 'false'
    } = req.query;
    
    // Validate parameters
    const parsedLimit = Math.min(parseInt(limit) || 100, 1000); // Max 1000 messages
    const parsedPartition = parseInt(partition) || -1;
    const isCaseSensitive = caseSensitive === 'true';
    
    logger.debug(`Searching messages in topic: ${topicName}, partition: ${parsedPartition}, limit: ${parsedLimit}, startFrom: ${startFrom}`);
    logger.debug(`Search filters - key: "${key}", value: "${value}", startTimestamp: ${startTimestamp}, endTimestamp: ${endTimestamp}, caseSensitive: ${isCaseSensitive}`);
    
    // Parse timestamps if provided
    let startTime = null;
    let endTime = null;
    
    if (startTimestamp) {
      startTime = new Date(startTimestamp).getTime();
      if (isNaN(startTime)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid startTimestamp format. Use ISO 8601 format (e.g., 2023-01-01T00:00:00Z)',
          data: []
        });
      }
    }
    
    if (endTimestamp) {
      endTime = new Date(endTimestamp).getTime();
      if (isNaN(endTime)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid endTimestamp format. Use ISO 8601 format (e.g., 2023-01-01T00:00:00Z)',
          data: []
        });
      }
    }
    
    // Validate timestamp range
    if (startTime && endTime && startTime > endTime) {
      return res.status(400).json({
        success: false,
        error: 'startTimestamp cannot be after endTimestamp',
        data: []
      });
    }
    
    const searchFilters = {
      key: key.trim(),
      value: value.trim(),
      startTimestamp: startTime,
      endTimestamp: endTime,
      caseSensitive: isCaseSensitive
    };
    
    const messages = await kafkaClient.searchMessages(
      topicName, 
      parsedPartition, 
      startFrom, 
      parsedLimit, 
      searchFilters
    );
    
    // Add metadata about the search
    const response = {
      success: true,
      data: messages,
      metadata: {
        topicName,
        partition: parsedPartition,
        limit: parsedLimit,
        startFrom,
        searchFilters,
        messageCount: messages.length,
        timestamp: new Date().toISOString()
      }
    };
    
    res.json(response);
  } catch (error) {
    logger.error(`Error searching messages for topic ${req.params.topicName}:`, error);
    res.status(500).json({
      success: false,
      error: error.message,
      data: []
    });
  }
});

module.exports = router; 