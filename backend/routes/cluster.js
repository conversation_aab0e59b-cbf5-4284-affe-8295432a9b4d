const express = require('express');
const kafkaClient = require('../kafka/dynamicKafkaClient');
const { authenticateToken } = require('../middleware/auth');
const { requireSuperAdmin, requirePermission, PERMISSIONS } = require('../middleware/rbac');
const logger = require('../utils/logger');

const router = express.Router();

// GET /api/cluster/info - Get cluster information and health (super admin only)
router.get('/info', authenticateToken, requireSuperAdmin, async (req, res) => {
  try {
    const clusterInfo = await kafkaClient.getClusterInfo();
    const isHealthy = await kafkaClient.isHealthy();
    
    res.json({
      success: true,
      data: {
        ...clusterInfo,
        health: {
          status: isHealthy ? 'healthy' : 'unhealthy',
          connected: isHealthy,
          timestamp: new Date().toISOString()
        }
      }
    });
  } catch (error) {
    logger.error('Error fetching cluster info:', error);

    // Return a default response instead of error
    res.json({
      success: false,
      data: {
        brokers: [],
        topics: 0,
        clusterId: 'unavailable',
        health: {
          status: 'unhealthy',
          connected: false,
          timestamp: new Date().toISOString(),
          error: 'Cluster information unavailable'
        }
      }
    });
  }
});

// GET /api/cluster/health - Get cluster health status (for backward compatibility)
router.get('/health', authenticateToken, async (req, res) => {
  try {
    const isHealthy = await kafkaClient.isHealthy();
    
    res.json({
      success: true,
      data: {
        status: isHealthy ? 'healthy' : 'unhealthy',
        connected: isHealthy,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('Error checking cluster health:', error);

    // Always return a response, even on error
    res.json({
      success: true,
      data: {
        status: 'unhealthy',
        connected: false,
        timestamp: new Date().toISOString(),
        error: 'Health check failed'
      }
    });
  }
});

module.exports = router; 