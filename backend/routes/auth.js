const express = require('express');
const router = express.Router();
const User = require('../models/User');
const { generateToken, authenticateToken, requireAdmin, loginRateLimit } = require('../middleware/auth');
const { requireSuperAdmin, requirePermission, PERMISSIONS } = require('../middleware/rbac');
const { rbacService } = require('../services/rbacService');
const logger = require('../utils/logger');
const jwt = require('jsonwebtoken');
const config = require('../config/config');
const bcrypt = require('bcryptjs'); // Added for password comparison
const { generateSecurePassword, extractUsernameFromEmail, validatePolicyBazaarEmail } = require('../utils/passwordGenerator');

// Register new user (super admin only)
router.post('/register', authenticateToken, requireSuperAdmin, async (req, res) => {
  try {
    const { email, role = 'TOPIC_VIEWER', assignedTopics = [], hasAllTopicsAccess = false } = req.body;

    // Validation
    if (!email) {
      return res.status(400).json({
        success: false,
        message: 'Email is required'
      });
    }

    // Validate email domain
    if (!validatePolicyBazaarEmail(email)) {
      return res.status(400).json({
        success: false,
        message: 'Email must be from @policybazaar.com domain'
      });
    }

    // Extract username from email
    const username = extractUsernameFromEmail(email);

    // Check if user already exists
    const existingUser = await User.findOne({
      $or: [{ email }, { username }]
    });

    if (existingUser) {
      return res.status(409).json({
        success: false,
        message: 'User with this email already exists'
      });
    }

    // Validate role
    const validRoles = ['SUPER_ADMIN', 'TOPIC_MANAGER', 'TOPIC_VIEWER'];
    if (!validRoles.includes(role)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid role specified'
      });
    }

    // Generate secure random password
    const generatedPassword = generateSecurePassword();

    // Create new user
    const user = new User({
      username,
      email,
      password: generatedPassword,
      role,
      assignedTopics: assignedTopics || [],
      hasAllTopicsAccess: hasAllTopicsAccess || false,
      requiresPasswordChange: true // Force password change on first login
    });

    await user.save();

    logger.debug(`New user registered: ${username} by admin: ${req.user.username}`);

    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      data: {
        user: {
          _id: user._id,
          username: user.username,
          email: user.email,
          role: user.role,
          assignedTopics: user.assignedTopics,
          hasAllTopicsAccess: user.hasAllTopicsAccess,
          requiresPasswordChange: user.requiresPasswordChange
        },
        generatedPassword // Return the generated password for admin to share
      }
    });
  } catch (error) {
    logger.error('Registration error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Login route
router.post('/login', loginRateLimit, async (req, res) => {
  try {
    const { username, password } = req.body;

    // Validate input
    if (!username || !password) {
      return res.status(400).json({
        success: false,
        message: 'Username and password are required'
      });
    }

    // Find user by username or email
    const user = await User.findOne({
      $or: [
        { username: username.trim() },
        { email: username.trim() }
      ]
    });
    
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

    // Check if user is active
    if (!user.isActive) {
      return res.status(401).json({
        success: false,
        message: 'Account is deactivated'
      });
    }

    // Verify password
    try {
      await user.comparePassword(password);
    } catch (error) {
      return res.status(401).json({
        success: false,
        message: error.message || 'Invalid credentials'
      });
    }

    // If password was reset by admin, set requiresPasswordChange to true after successful login
    if (user.passwordResetByAdmin) {
      user.requiresPasswordChange = true;
      user.passwordResetByAdmin = false; // Reset the flag
      await user.save();
    }

    // Generate JWT token using middleware function
    const token = generateToken(user._id);

    // Get user permissions
    const permissions = await rbacService.getUserPermissions(user._id);

    logger.debug(`User logged in: ${user.username}`);

    res.json({
      success: true,
      message: 'Login successful',
      data: {
        user: {
          _id: user._id,
          username: user.username,
          email: user.email,
          role: user.role,
          assignedTopics: user.assignedTopics,
          hasAllTopicsAccess: user.hasAllTopicsAccess,
          requiresPasswordChange: user.requiresPasswordChange
        },
        token,
        permissions
      }
    });
  } catch (error) {
    logger.error('Login error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Change password route (for current user)
router.post('/change-password', authenticateToken, async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;

    // Validate input
    if (!newPassword) {
      return res.status(400).json({
        success: false,
        message: 'New password is required'
      });
    }

    if (newPassword.length < 6) {
      return res.status(400).json({
        success: false,
        message: 'New password must be at least 6 characters long'
      });
    }

    // Get current user
    const user = await User.findById(req.user._id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // For first-time password change, current password is not required
    if (user.requiresPasswordChange) {
      // Skip current password verification for first-time changes
    } else {
      // For regular password changes, current password is required
      if (!currentPassword) {
        return res.status(400).json({
          success: false,
          message: 'Current password is required'
        });
      }

      // Verify current password
      try {
        await user.comparePassword(currentPassword);
      } catch (error) {
        return res.status(401).json({
          success: false,
          message: 'Current password is incorrect'
        });
      }
    }

    // Check if new password is same as current
    const isSamePassword = await bcrypt.compare(newPassword, user.password);
    if (isSamePassword) {
      return res.status(400).json({
        success: false,
        message: 'New password must be different from current password'
      });
    }

    // Change password
    await user.changePassword(newPassword);

    logger.debug(`Password changed for user: ${user.username}`);

    res.json({
      success: true,
      message: 'Password changed successfully'
    });
  } catch (error) {
    logger.error('Change password error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Reset password route (admin only)
router.post('/reset-password/:userId', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { userId } = req.params;
    const { newPassword, generateRandom = true, passwordData } = req.body;

    // Handle both direct parameters and nested passwordData object
    const actualNewPassword = passwordData?.newPassword || newPassword;
    const actualGenerateRandom = passwordData?.generateRandom !== undefined ? passwordData.generateRandom : generateRandom;

    let passwordToSet;

    if (actualGenerateRandom) {
      // Use the password provided by frontend (which was already generated there)
      if (!actualNewPassword) {
        return res.status(400).json({
          success: false,
          message: 'New password is required'
        });
      }

      if (actualNewPassword.length < 6) {
        return res.status(400).json({
          success: false,
          message: 'Password must be at least 6 characters long'
        });
      }

      passwordToSet = actualNewPassword;
    } else {
      // Use provided password
      if (!actualNewPassword) {
        return res.status(400).json({
          success: false,
          message: 'New password is required when not generating random password'
        });
      }

      if (actualNewPassword.length < 6) {
        return res.status(400).json({
          success: false,
          message: 'Password must be at least 6 characters long'
        });
      }

      passwordToSet = actualNewPassword;
    }

    // Get user to reset
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Reset password
    await user.resetPassword(passwordToSet);

    logger.debug(`Password reset for user: ${user.username} by admin: ${req.user.username}`);

    res.json({
      success: true,
      message: 'Password reset successfully. User can login with the new password and will be prompted to change it.',
      data: {
        resetPassword: passwordToSet
      }
    });
  } catch (error) {
    logger.error('Reset password error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Get current user profile
router.get('/profile', authenticateToken, async (req, res) => {
  try {
    res.json({
      success: true,
      data: req.user
    });
  } catch (error) {
    logger.error('Profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});



// List all users (admin only)
router.get('/users', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const users = await User.find({}, '-password').sort({ createdAt: -1 });

    res.json({
      success: true,
      data: users
    });
  } catch (error) {
    logger.error('List users error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Update user (admin only)
router.put('/users/:userId', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { userId } = req.params;
    const { role, isActive, password, email, username } = req.body;

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Update basic fields
    if (username !== undefined) {
      user.username = username;
    }

    if (email !== undefined) {
      user.email = email;
    }

    if (role !== undefined) {
      user.role = role;
    }

    if (isActive !== undefined) {
      user.isActive = isActive;
    }

    // Update password only if provided (for editing users)
    if (password && password.trim()) {
      user.password = password;
    }

    await user.save();

    logger.debug(`User updated: ${user.username} by admin: ${req.user.username}`);

    res.json({
      success: true,
      message: 'User updated successfully',
      data: {
        _id: user._id,
        username: user.username,
        email: user.email,
        role: user.role,
        isActive: user.isActive,
        assignedTopics: user.assignedTopics,
        hasAllTopicsAccess: user.hasAllTopicsAccess,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      }
    });
  } catch (error) {
    logger.error('Update user error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Delete user (admin only)
router.delete('/users/:userId', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { userId } = req.params;

    if (userId === req.user._id.toString()) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete your own account'
      });
    }

    const user = await User.findByIdAndDelete(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    logger.debug(`User deleted: ${user.username} by admin: ${req.user.username}`);

    res.json({
      success: true,
      message: 'User deleted successfully'
    });
  } catch (error) {
    logger.error('Delete user error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Logout (client-side token removal, but we can track it server-side if needed)
router.post('/logout', authenticateToken, async (req, res) => {
  try {
    logger.debug(`User logged out: ${req.user.username}`);
    
    res.json({
      success: true,
      message: 'Logout successful'
    });
  } catch (error) {
    logger.error('Logout error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});



// Get all available roles (super admin only)
router.get('/roles', authenticateToken, requireSuperAdmin, async (req, res) => {
  try {
    const roles = await rbacService.getAllRoles();

    res.json({
      success: true,
      data: roles
    });
  } catch (error) {
    logger.error('Error fetching roles:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch roles'
    });
  }
});

// Update user role and topic assignments (super admin only)
router.put('/users/:userId/role', authenticateToken, requireSuperAdmin, async (req, res) => {
  try {
    const { userId } = req.params;
    const { role, assignedTopics = [], hasAllTopicsAccess = false, password, email, username } = req.body;

    // Validate role
    const validRoles = ['SUPER_ADMIN', 'TOPIC_MANAGER', 'TOPIC_VIEWER'];
    if (!validRoles.includes(role)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid role specified'
      });
    }

    // Get the user
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Update basic fields
    if (username !== undefined) {
      user.username = username;
    }

    if (email !== undefined) {
      user.email = email;
    }

    // Update password only if provided
    if (password && password.trim()) {
      user.password = password;
    }

    // Update user role
    await rbacService.updateUserRole(userId, role);

    // Update topic assignments
    const updatedUser = await rbacService.updateUserTopicAssignments(userId, assignedTopics, hasAllTopicsAccess);

    logger.debug(`User ${updatedUser.username} role updated to ${role} by admin: ${req.user.username}`);

    res.json({
      success: true,
      message: 'User role and permissions updated successfully',
      data: updatedUser
    });
  } catch (error) {
    logger.error('Error updating user role:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to update user role'
    });
  }
});

// Get user permissions (for current user)
router.get('/permissions', authenticateToken, async (req, res) => {
  try {
    const permissions = await rbacService.getUserPermissions(req.user.id);

    res.json({
      success: true,
      data: permissions
    });
  } catch (error) {
    logger.error('Error fetching user permissions:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch permissions'
    });
  }
});

module.exports = router;