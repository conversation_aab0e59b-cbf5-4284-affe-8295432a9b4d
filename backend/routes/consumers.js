const express = require('express');
const kafkaClient = require('../kafka/dynamicKafkaClient');
const logger = require('../utils/logger');
const { authenticateToken } = require('../middleware/auth');
const {
  requirePermission,
  filterTopicsByAccess,
  PERMISSIONS
} = require('../middleware/rbac');
const { rbacService } = require('../services/rbacService');

const router = express.Router();

// GET /api/consumers - List consumer groups based on user access
router.get('/', authenticateToken, filterTopicsByAccess, async (req, res) => {
  try {
    const allConsumerGroups = await kafkaClient.getConsumerGroups();

    // Filter consumer groups based on user's topic access
    const userPermissions = await rbacService.getUserPermissions(req.user.id);
    let accessibleConsumerGroups = [];

    if (userPermissions.role === 'SUPER_ADMIN') {
      accessibleConsumerGroups = allConsumerGroups;
    } else {
      // Filter consumer groups based on topics they consume
      for (const group of allConsumerGroups) {
        if (await rbacService.hasConsumerGroupAccess(req.user.id, group)) {
          accessibleConsumerGroups.push(group);
        }
      }
    }

    res.json({
      success: true,
      data: accessibleConsumerGroups,
      count: accessibleConsumerGroups.length
    });
  } catch (error) {
    logger.error('Error fetching consumer groups:', error);
    res.status(500).json({
      error: {
        message: 'Failed to fetch consumer groups',
        status: 500
      }
    });
  }
});

// GET /api/consumers/:groupId - Get consumer group details
router.get('/:groupId', authenticateToken, async (req, res) => {
  try {
    const { groupId } = req.params;
    const groupDetails = await kafkaClient.getConsumerGroupDetails(groupId);
    
    // Check if user has access to this consumer group
    const userPermissions = await rbacService.getUserPermissions(req.user.id);
    if (userPermissions.role !== 'SUPER_ADMIN') {
      const hasAccess = await rbacService.hasConsumerGroupAccess(req.user.id, groupDetails);
      if (!hasAccess) {
        return res.status(403).json({
          success: false,
          message: 'Access denied: You do not have permission to view this consumer group'
        });
      }
    }
    
    res.json({
      success: true,
      data: groupDetails
    });
  } catch (error) {
    logger.error('Error fetching consumer group details:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to fetch consumer group details',
        status: 500
      }
    });
  }
});

// DELETE /api/consumers/:groupId - Delete consumer group (REMOVED FOR VAPT COMPLIANCE)

module.exports = router; 