const { hasExceededDailyLimit, getRemainingDailyCount, incrementUserDailyMessageCount } = require('../utils/messageTracker');
const logger = require('../utils/logger');

/**
 * Middleware to check daily message production limit
 */
const checkDailyMessageLimit = (req, res, next) => {
  const userId = req.user.id;
  
  if (hasExceededDailyLimit(userId)) {
    return res.status(429).json({
      success: false,
      error: {
        message: 'Daily message production limit exceeded (1000 messages per day)',
        status: 429,
        remainingCount: 0
      }
    });
  }
  
  next();
};

/**
 * Middleware to check and increment message count for single message
 */
const checkAndIncrementSingleMessage = (req, res, next) => {
  const userId = req.user.id;
  
  logger.debug(`Single message middleware - User ID: ${userId}, Username: ${req.user.username}`);
  
  if (hasExceededDailyLimit(userId)) {
    logger.warn(`User ${req.user.username} exceeded daily limit`);
    return res.status(429).json({
      success: false,
      error: {
        message: 'Daily message production limit exceeded (1000 messages per day)',
        status: 429,
        remainingCount: 0
      }
    });
  }
  
  // Increment count for single message
  const newCount = incrementUserDailyMessageCount(userId, 1);
  const remainingCount = getRemainingDailyCount(userId);
  
  logger.debug(`Setting X-Remaining-Messages header to: ${remainingCount}`);
  logger.debug(`Response headers after setting: ${JSON.stringify(res.getHeaders())}`);
  
  // Add remaining count to response headers
  res.set('X-Remaining-Messages', remainingCount.toString());
  
  logger.info(`User ${req.user.username} produced message. Daily count: ${newCount}/1000`);
  
  next();
};

/**
 * Middleware to check and increment message count for bulk messages
 */
const checkAndIncrementBulkMessages = (req, res, next) => {
  const userId = req.user.id;
  const messageCount = req.body.messages ? req.body.messages.length : 0;
  
  logger.debug(`Bulk message middleware - User ID: ${userId}, Username: ${req.user.username}, Message count: ${messageCount}`);
  
  if (messageCount === 0) {
    return res.status(400).json({
      success: false,
      error: {
        message: 'No messages provided',
        status: 400
      }
    });
  }
  
  if (hasExceededDailyLimit(userId)) {
    logger.warn(`User ${req.user.username} exceeded daily limit`);
    return res.status(429).json({
      success: false,
      error: {
        message: 'Daily message production limit exceeded (1000 messages per day)',
        status: 429,
        remainingCount: 0
      }
    });
  }
  
  const remainingCount = getRemainingDailyCount(userId);
  
  if (messageCount > remainingCount) {
    logger.warn(`User ${req.user.username} tried to produce ${messageCount} messages but only has ${remainingCount} remaining`);
    return res.status(429).json({
      success: false,
      error: {
        message: `Cannot produce ${messageCount} messages. Only ${remainingCount} messages remaining today.`,
        status: 429,
        remainingCount
      }
    });
  }
  
  // Increment count for bulk messages
  const newCount = incrementUserDailyMessageCount(userId, messageCount);
  const newRemainingCount = getRemainingDailyCount(userId);
  
  logger.debug(`Setting X-Remaining-Messages header to: ${newRemainingCount}`);
  
  // Add remaining count to response headers
  res.set('X-Remaining-Messages', newRemainingCount.toString());
  
  logger.info(`User ${req.user.username} produced ${messageCount} messages. Daily count: ${newCount}/1000`);
  
  next();
};

module.exports = {
  checkDailyMessageLimit,
  checkAndIncrementSingleMessage,
  checkAndIncrementBulkMessages
}; 