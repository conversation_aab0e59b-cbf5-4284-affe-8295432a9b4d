const { Role, PERMISSIONS } = require('../models/Role');
const User = require('../models/User');
const logger = require('../utils/logger');

class RBACService {
  constructor() {
    this.permissionCache = new Map();
  }

  /**
   * Initialize RBAC system - create default roles
   */
  async initialize() {
    try {
      await Role.initializeDefaultRoles();
      logger.info('RBAC system initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize RBAC system:', error);
      throw error;
    }
  }

  /**
   * Get user permissions based on their role
   */
  async getUserPermissions(userId) {
    try {
      // Check cache first
      if (this.permissionCache.has(userId)) {
        return this.permissionCache.get(userId);
      }

      const user = await User.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      const role = await Role.findOne({ name: user.role });
      if (!role) {
        throw new Error('Role not found');
      }

      const permissions = {
        role: user.role,
        permissions: role.permissions,
        assignedTopics: user.assignedTopics || [],
        hasAllTopicsAccess: user.hasAllTopicsAccess || false
      };

      // Cache permissions for 5 minutes
      this.permissionCache.set(userId, permissions);
      setTimeout(() => {
        this.permissionCache.delete(userId);
      }, 5 * 60 * 1000);

      return permissions;
    } catch (error) {
      logger.error('Error getting user permissions:', error);
      throw error;
    }
  }

  /**
   * Check if user has specific permission
   */
  async hasPermission(userId, permission) {
    try {
      const userPermissions = await this.getUserPermissions(userId);
      return userPermissions.permissions.includes(permission);
    } catch (error) {
      logger.error('Error checking permission:', error);
      return false;
    }
  }

  /**
   * Check if user has access to specific topic
   */
  async hasTopicAccess(userId, topicName) {
    try {
      const userPermissions = await this.getUserPermissions(userId);
      
      // Super admin has access to all topics
      if (userPermissions.role === 'SUPER_ADMIN') {
        return true;
      }

      // User with all topics access
      if (userPermissions.hasAllTopicsAccess) {
        return true;
      }

      // Check if topic is in assigned topics
      return userPermissions.assignedTopics.includes(topicName);
    } catch (error) {
      logger.error('Error checking topic access:', error);
      return false;
    }
  }

  /**
   * Get topics user has access to
   */
  async getUserAccessibleTopics(userId, allTopics = []) {
    try {
      const userPermissions = await this.getUserPermissions(userId);
      
      // Super admin has access to all topics
      if (userPermissions.role === 'SUPER_ADMIN') {
        return allTopics;
      }

      // User with all topics access
      if (userPermissions.hasAllTopicsAccess) {
        return allTopics;
      }

      // Filter topics based on assigned topics
      return allTopics.filter(topic => 
        userPermissions.assignedTopics.includes(topic.name || topic)
      );
    } catch (error) {
      logger.error('Error getting user accessible topics:', error);
      return [];
    }
  }

  /**
   * Check if user can access consumer group (based on topics it consumes)
   */
  async hasConsumerGroupAccess(userId, consumerGroup, topicsList = []) {
    try {
      const userPermissions = await this.getUserPermissions(userId);
      
      // Super admin has access to all consumer groups
      if (userPermissions.role === 'SUPER_ADMIN') {
        return true;
      }

      // For topic managers and viewers, check if they have access to any topic the consumer group consumes
      if (consumerGroup.topics && consumerGroup.topics.length > 0) {
        for (const topic of consumerGroup.topics) {
          if (await this.hasTopicAccess(userId, topic)) {
            return true;
          }
        }
      }

      // If no topics found in consumer group, check if user has all topics access
      if (userPermissions.hasAllTopicsAccess) {
        return true;
      }

      return false;
    } catch (error) {
      logger.error('Error checking consumer group access:', error);
      return false;
    }
  }

  /**
   * Update user topic assignments
   */
  async updateUserTopicAssignments(userId, assignedTopics, hasAllTopicsAccess = false) {
    try {
      const user = await User.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      user.assignedTopics = assignedTopics || [];
      user.hasAllTopicsAccess = hasAllTopicsAccess;
      await user.save();

      // Clear cache for this user
      this.permissionCache.delete(userId);

      logger.info(`Updated topic assignments for user ${user.username}`);
      return user;
    } catch (error) {
      logger.error('Error updating user topic assignments:', error);
      throw error;
    }
  }

  /**
   * Update user role
   */
  async updateUserRole(userId, newRole) {
    try {
      const user = await User.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Validate role exists
      const role = await Role.findOne({ name: newRole });
      if (!role) {
        throw new Error('Invalid role');
      }

      user.role = newRole;
      await user.save();

      // Clear cache for this user
      this.permissionCache.delete(userId);

      logger.info(`Updated role for user ${user.username} to ${newRole}`);
      return user;
    } catch (error) {
      logger.error('Error updating user role:', error);
      throw error;
    }
  }

  /**
   * Clear permissions cache for user
   */
  clearUserCache(userId) {
    this.permissionCache.delete(userId);
  }

  /**
   * Clear all permissions cache
   */
  clearAllCache() {
    this.permissionCache.clear();
  }

  /**
   * Get all available roles
   */
  async getAllRoles() {
    try {
      return await Role.find({ isActive: true }).sort({ name: 1 });
    } catch (error) {
      logger.error('Error getting all roles:', error);
      throw error;
    }
  }

  /**
   * Get all permissions
   */
  getAllPermissions() {
    return PERMISSIONS;
  }
}

// Create singleton instance
const rbacService = new RBACService();

module.exports = {
  rbacService,
  PERMISSIONS
};
