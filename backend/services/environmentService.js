const config = require('../config/config');
const logger = require('../utils/logger');

class EnvironmentService {
  constructor() {
    this.availableEnvironments = {
      qa: {
        name: 'QA Environment',
        description: 'Quality Assurance - Single Broker',
        kafka: {
          brokers: process.env.KAFKA_QA_BROKERS.split(','),
          clientId: 'kafka-dashboard-qa',
          groupId: 'kafka-dashboard-group-qa',
          connectionTimeout: 5000,
          requestTimeout: 30000,
          ssl: false,
          sasl: {
            mechanism: process.env.KAFKA_QA_SASL_MECHANISM,
            username: process.env.KAFKA_QA_SASL_USERNAME,
            password: process.env.KAFKA_QA_SASL_PASSWORD
          }
        }
      },
      prod: {
        name: 'Production Environment',
        description: 'Production - Multiple Brokers',
        kafka: {
          brokers: process.env.KAFKA_PROD_BROKERS.split(','),
          clientId: 'kafka-dashboard-prod',
          groupId: 'kafka-dashboard-group-prod',
          connectionTimeout: 5000,
          requestTimeout: 30000,
          ssl: false,
          sasl: {
            mechanism: process.env.KAFKA_PROD_SASL_MECHANISM,
            username: process.env.KAFKA_PROD_SASL_USERNAME,
            password: process.env.KAFKA_PROD_SASL_PASSWORD
          }
        }
      }
    };
    
    this.currentEnvironment = 'prod'; // Default to PROD
  }

  /**
   * Get all available environments
   */
  getAvailableEnvironments() {
    return Object.keys(this.availableEnvironments).map(key => ({
      key,
      name: this.availableEnvironments[key].name,
      description: this.availableEnvironments[key].description,
      brokerCount: this.availableEnvironments[key].kafka.brokers.length
    }));
  }

  /**
   * Get current environment
   */
  getCurrentEnvironment() {
    return {
      key: this.currentEnvironment,
      ...this.availableEnvironments[this.currentEnvironment]
    };
  }

  /**
   * Switch to a different environment
   */
  switchEnvironment(environmentKey) {
    if (!this.availableEnvironments[environmentKey]) {
      throw new Error(`Invalid environment: ${environmentKey}`);
    }

    const previousEnv = this.currentEnvironment;
    this.currentEnvironment = environmentKey;
    
    logger.info(`Environment switched from ${previousEnv} to ${environmentKey}`);
    
    return this.getCurrentEnvironment();
  }

  /**
   * Get Kafka configuration for current environment
   */
  getCurrentKafkaConfig() {
    return this.availableEnvironments[this.currentEnvironment].kafka;
  }

  /**
   * Get Kafka configuration for specific environment
   */
  getKafkaConfig(environmentKey) {
    if (!this.availableEnvironments[environmentKey]) {
      throw new Error(`Invalid environment: ${environmentKey}`);
    }
    return this.availableEnvironments[environmentKey].kafka;
  }

  /**
   * Validate environment key
   */
  isValidEnvironment(environmentKey) {
    return this.availableEnvironments.hasOwnProperty(environmentKey);
  }
}

// Create singleton instance
const environmentService = new EnvironmentService();

module.exports = environmentService;
