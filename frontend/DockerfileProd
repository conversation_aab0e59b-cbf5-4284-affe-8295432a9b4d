FROM 721537467949.dkr.ecr.ap-south-1.amazonaws.com/node-22-alpine AS build

WORKDIR /app

# Copy package files and install dependencies
COPY package*.json ./
RUN npm install

# Copy all files and build the React app
COPY . .
RUN npm run build


FROM 721537467949.dkr.ecr.ap-south-1.amazonaws.com/nginx-1.28-alpine

# Remove default Nginx config
RUN rm /etc/nginx/conf.d/default.conf

# Copy built files from build stage
COPY --from=build /app/build /usr/share/nginx/html

# Optional: Custom Nginx config (adjust if needed)
COPY nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
