server {

    listen 80;

     access_log off;

  # Security Headers:-
    
       add_header X-XSS-Protection "1; mode=block";
       add_header X-Content-Type-Options nosniff;
     # add_header X-Frame-Options "SAMEORIGIN";
       add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
    
   # Header cookie size increase:-
      large_client_header_buffers 4 16k;
   # Hide Nginx version:-

       server_tokens off;
    
    gzip on;
    gzip_http_version 1.0;
    gzip_comp_level 5;
    gzip_min_length 1100;
    gzip_buffers 16 8k;
    gzip_proxied any;
    gzip_types text/html text/css text/javascript text/xml text/plain text/x-component application/javascript application/x-javascript application/json application/xml application/rss+xml font/truetype font/opentype application/vnd.ms-fontobject image/svg+xml image/x-icon image/bmp;
    gzip_static on;
    gzip_proxied expired no-cache no-store private auth;
    gzip_disable "MSIE [1-6]\.";
    gzip_vary on;


    sendfile on;
    sendfile_max_chunk 1m;
    tcp_nopush on;


    location ~* /.+.(woff|woff2|webp|svg|gif|jpg|jpeg|png|ico|wmv|3gp|avi|mpg|mpeg|mp4|flv|mp3|js|json|css)$ {
        root /usr/share/nginx/html/;
    }    
    location ~* /Content/insurerLogo/.+.(woff|woff2|webp|svg|gif|jpg|jpeg|png|ico|wmv|3gp|avi|mpg|mpeg|mp4|flv|mp3|js|json|css)$ {
        root /usr/share/nginx/html/;
    }


    location / {
       autoindex off;
        root /usr/share/nginx/html/;
        index index.html index.htm;
        try_files $uri $uri/ /index.html;
    }


    error_page 500 502 503 504 /50x.html;

    location = /50x.html {
        root /usr/share/nginx/html/;
    }

}
