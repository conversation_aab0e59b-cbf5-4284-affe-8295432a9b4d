import React from 'react';
import { useNavigate } from 'react-router-dom';
import Login from '../pages/Login';
import rbacApi from '../services/rbacApi';

const LoginWrapper = ({ onLogin }) => {
  const navigate = useNavigate();

  const handleLoginSuccess = async (userData, token) => {
    // Call the original onLogin handler
    onLogin(userData, token);

    // Dispatch custom login event to notify other components
    window.dispatchEvent(new CustomEvent('user-login', { detail: { userData, token } }));

    try {
      // Add a small delay to ensure token is properly set
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // Fetch user permissions to determine redirect
      const response = await rbacApi.getUserPermissions();
      const permissions = response.data;

      // Redirect based on role
      if (permissions.role === 'SUPER_ADMIN') {
        navigate('/', { replace: true });
      } else {
        // Topic Managers and Topic Viewers go to Topics page
        navigate('/topics', { replace: true });
      }
    } catch (error) {
      console.error('Error fetching permissions for redirect:', error);
      // If we can't fetch permissions, check user role from login response
      if (userData.role === 'SUPER_ADMIN') {
        navigate('/', { replace: true });
      } else {
        navigate('/topics', { replace: true });
      }
    }
  };

  return <Login onLogin={handleLoginSuccess} />;
};

export default LoginWrapper;
