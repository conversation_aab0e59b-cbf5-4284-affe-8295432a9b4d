import React, { useState } from 'react';
import {
  <PERSON>alog,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Box,
  Typography,
  Alert,
  CircularProgress,
  InputAdornment,
  IconButton,
  Stack,
  FormControlLabel,
  Checkbox,
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  Lock,
  Security,
  ContentCopy,
  Refresh,
} from '@mui/icons-material';
import toast from 'react-hot-toast';

const ResetPasswordDialog = ({ open, onClose, onSubmit, user, isLoading }) => {
  const [formData, setFormData] = useState({
    newPassword: '',
    confirmPassword: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [errors, setErrors] = useState({});
  const [generateRandom, setGenerateRandom] = useState(true);
  const [generatedPassword, setGeneratedPassword] = useState('');

  const handleChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const generateRandomPassword = () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
    let password = '';
    for (let i = 0; i < 12; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    setGeneratedPassword(password);
    setFormData(prev => ({
      ...prev,
      newPassword: password,
      confirmPassword: password
    }));
  };

  const copyToClipboard = async (text) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success('Password copied to clipboard!');
    } catch (err) {
      toast.error('Failed to copy password');
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!generateRandom) {
      if (!formData.newPassword.trim()) {
        newErrors.newPassword = 'New password is required';
      } else if (formData.newPassword.length < 6) {
        newErrors.newPassword = 'Password must be at least 6 characters long';
      }

      if (!formData.confirmPassword.trim()) {
        newErrors.confirmPassword = 'Please confirm your password';
      } else if (formData.newPassword !== formData.confirmPassword) {
        newErrors.confirmPassword = 'Passwords do not match';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (!validateForm()) return;

    const passwordToSubmit = generateRandom ? generatedPassword : formData.newPassword;
    onSubmit(passwordToSubmit, generateRandom);
  };

  const handleClose = () => {
    if (!isLoading) {
      setFormData({
        newPassword: '',
        confirmPassword: '',
      });
      setErrors({});
      setGenerateRandom(true);
      setGeneratedPassword('');
      onClose();
    }
  };

  // Generate password on mount if generateRandom is true
  React.useEffect(() => {
    if (open && generateRandom) {
      generateRandomPassword();
    }
  }, [open, generateRandom]);

  return (
    <Dialog 
      open={open} 
      onClose={handleClose}
      maxWidth="sm" 
      fullWidth
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Security color="warning" />
          <Typography variant="h6">
            Reset Password for {user?.username}
          </Typography>
        </Box>
      </DialogTitle>
      
      <DialogContent>
        <Box sx={{ py: 1 }}>
          <Alert severity="warning" sx={{ mb: 3 }}>
            <Typography variant="body2">
              <strong>Important:</strong> This will reset the password for user "{user?.username}". 
              The user will be required to change their password on their next login for security reasons.
            </Typography>
          </Alert>

          <FormControlLabel
            control={
              <Checkbox
                checked={generateRandom}
                onChange={(e) => setGenerateRandom(e.target.checked)}
              />
            }
            label="Generate secure random password"
            sx={{ mb: 2 }}
          />

          {generateRandom ? (
            <Stack spacing={2}>
              <Box>
                <Typography variant="subtitle2" gutterBottom>
                  Generated Password:
                </Typography>
                <Box sx={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  gap: 1,
                  p: 2,
                  border: '1px solid',
                  borderColor: 'divider',
                  borderRadius: 1,
                  bgcolor: 'background.paper'
                }}>
                  <Typography 
                    variant="body1" 
                    sx={{ 
                      fontFamily: 'monospace',
                      flexGrow: 1,
                      userSelect: 'all'
                    }}
                  >
                    {generatedPassword}
                  </Typography>
                  <IconButton
                    onClick={() => copyToClipboard(generatedPassword)}
                    size="small"
                    color="primary"
                  >
                    <ContentCopy />
                  </IconButton>
                  <IconButton
                    onClick={generateRandomPassword}
                    size="small"
                    color="secondary"
                  >
                    <Refresh />
                  </IconButton>
                </Box>
                <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                  Click the copy button to copy the password, then share it with the user securely.
                </Typography>
              </Box>
            </Stack>
          ) : (
            <Stack spacing={3}>
              <TextField
                label="New Temporary Password"
                type={showPassword ? 'text' : 'password'}
                value={formData.newPassword}
                onChange={(e) => handleChange('newPassword', e.target.value)}
                error={!!errors.newPassword}
                helperText={errors.newPassword || "This will be the temporary password the user will use to login"}
                fullWidth
                required
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Lock color="action" />
                    </InputAdornment>
                  ),
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => setShowPassword(!showPassword)}
                        edge="end"
                      >
                        {showPassword ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />
              
              <TextField
                label="Confirm New Password"
                type={showConfirmPassword ? 'text' : 'password'}
                value={formData.confirmPassword}
                onChange={(e) => handleChange('confirmPassword', e.target.value)}
                error={!!errors.confirmPassword}
                helperText={errors.confirmPassword}
                fullWidth
                required
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Lock color="action" />
                    </InputAdornment>
                  ),
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        edge="end"
                      >
                        {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />
            </Stack>
          )}
        </Box>
      </DialogContent>
      
      <DialogActions sx={{ p: 3, pt: 1 }}>
        <Button
          onClick={handleClose}
          disabled={isLoading}
        >
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          color="warning"
          disabled={isLoading}
          startIcon={
            isLoading ? (
              <CircularProgress size={20} />
            ) : (
              <Security />
            )
          }
          sx={{ minWidth: 120 }}
        >
          {isLoading ? 'Resetting...' : 'Reset Password'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ResetPasswordDialog; 