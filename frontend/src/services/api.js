import axios from 'axios';

/**
 * API URL Configuration with Environment Detection
 * 
 * Browser-Safe Environment Detection:
 * - Use window.location.hostname to detect environment
 * - No dependency on process.env at runtime
 * 
 * Manual Override for Local Development:
 * - Set MANUAL_API_URL to override automatic detection
 * - Useful for testing different environments locally
 * 
 * Test Scenarios:
 * 1. Production (kafkadashboard.policybazaar.com): Uses https://kafkadashboard.policybazaar.com/api
 * 2. Development on localhost: Uses http://localhost:5000/api
 * 3. Development on remote domain: Uses /api (proxied to production)
 */

// Manual override for local development (set to null to use automatic detection)
const MANUAL_API_URL = null; // Change to 'https://kafkadashboard.policybazaar.com/api' to test production locally

// Browser-safe environment detection
const hostname = window.location.hostname;
const isLocalhost = hostname === 'localhost' || hostname === '127.0.0.1';
const isProduction = hostname === 'kafkadashboard.policybazaar.com' || hostname.includes('policybazaar.com');

// API URL determination logic
let API_BASE_URL;

if (MANUAL_API_URL) {
  // Manual override takes precedence
  API_BASE_URL = MANUAL_API_URL;
} else if (isProduction) {
  // Production: Always use the production domain
  API_BASE_URL = 'https://kafkadashboard.policybazaar.com/api';
} else if (isLocalhost) {
  // Development on localhost: Use localhost backend
  API_BASE_URL = 'http://localhost:5000/api';
} else {
  // Development on remote domain: Use proxy (relative URLs)
  // The proxy in package.json will handle this
  API_BASE_URL = '/api';
}

// Log the API configuration for debugging (only in development)
if (!isProduction) {
  console.log('API Configuration:', {
    hostname,
    isLocalhost,
    isProduction,
    manualOverride: MANUAL_API_URL,
    apiBaseUrl: API_BASE_URL
  });
}

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add auth headers
    const token = localStorage.getItem('kafka_dashboard_auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    if (error.response) {
      // Handle auth errors
      if (error.response.status === 401) {
        localStorage.removeItem('kafka_dashboard_auth_token');
        localStorage.removeItem('kafka_dashboard_user');
        // Only redirect if we're not already on the login page
        if (!window.location.pathname.includes('/login')) {
          window.location.href = '/login';
        }
      }
      // Server responded with error status
      throw new Error(error.response.data.message || error.response.data.error?.message || 'Server error');
    } else if (error.request) {
      // Request made but no response
      console.log('No response from server', error.request);
      console.log('Error: ',error.message);
      throw new Error('No response from server');
    } else {
      // Something else happened
      throw new Error(error.message || 'Unknown error');
    }
  }
);

// Topics API
export const topicsApi = {
  getAll: (params = {}) => api.get('/topics', { params }),
  getById: (topicName) => api.get(`/topics/${topicName}`),
  // create: (topicData) => api.post('/topics', topicData), // REMOVED FOR VAPT COMPLIANCE
  // delete: (topicName) => api.delete(`/topics/${topicName}`), // REMOVED FOR VAPT COMPLIANCE
  // addPartitions: (topicName, partitionCount) => // REMOVED FOR VAPT COMPLIANCE
  //   api.post(`/topics/${topicName}/partitions`, { partitionCount }),
  getMessages: (topicName, params) => 
    api.get(`/topics/${topicName}/messages`, { params }),
  searchMessages: (topicName, params) => 
    api.get(`/topics/${topicName}/search`, { params }),
  produceMessage: (topicName, message) => 
    api.post(`/topics/${topicName}/messages`, message),
  produceMessagesBulk: (topicName, messages, batchSize) => 
    api.post(`/topics/${topicName}/messages/bulk`, { messages, batchSize }),
  subscribe: (topicName) => api.post(`/topics/${topicName}/subscribe`),
  unsubscribe: (topicName) => api.post(`/topics/${topicName}/unsubscribe`),
  getMessageCount: (topicName) => api.get(`/topics/${topicName}/message-count`),
  getTopicConfig: (topicName) => api.get(`/topics/${topicName}/config`),
};

// Consumer Groups API
export const consumerGroupsApi = {
  getAll: async () => {
    const response = await api.get('/consumers');
    return response.data || response;
  },
  getById: async (groupId) => {
    const response = await api.get(`/consumers/${groupId}`);
    return response.data || response;
  },
  // delete: (groupId) => api.delete(`/consumers/${groupId}`), // REMOVED FOR VAPT COMPLIANCE
};

// Cluster API
export const clusterApi = {
  getInfo: async () => {
    const response = await api.get('/cluster/info');
    return response.data || response;
  },
};

// Auth API
export const authApi = {
  login: (credentials) => api.post('/auth/login', credentials),
  logout: () => api.post('/auth/logout'),
  getProfile: () => api.get('/auth/profile'),
  register: (userData) => api.post('/auth/register', userData),
  getUsers: () => api.get('/auth/users'),
  updateUser: (userId, userData) => api.put(`/auth/users/${userId}`, userData),
  deleteUser: (userId) => api.delete(`/auth/users/${userId}`),
  getRoles: () => api.get('/auth/roles'),
  getPermissions: () => api.get('/auth/permissions'),
  updateUserRole: (userId, roleData) => api.put(`/auth/users/${userId}/role`, roleData),
  changePassword: (passwordData) => api.post('/auth/change-password', passwordData),
  resetPassword: (userId, passwordData) => api.post(`/auth/reset-password/${userId}`, passwordData),
};

// Environment API
export const environmentApi = {
  getAvailableEnvironments: () => api.get('/environment/environments'),
  getCurrentEnvironment: () => api.get('/environment/current'),
  switchEnvironment: (environment) => api.post('/environment/switch', { environment }),
  testConnection: (environment) => api.post('/environment/test-connection', { environment }),
};

export default api;