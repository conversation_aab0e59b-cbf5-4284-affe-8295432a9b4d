import io from 'socket.io-client';

const SOCKET_URL = process.env.REACT_APP_SOCKET_URL || 'http://localhost:5000';

class SocketService {
  constructor() {
    this.socket = null;
    this.listeners = new Map();
  }

  connect() {
    if (this.socket?.connected) {
      return this.socket;
    }

    this.socket = io(SOCKET_URL, {
      transports: ['websocket'],
      upgrade: false,
    });

    this.socket.on('connect', () => {
      // Connection successful - no need to log in production
    });

    this.socket.on('disconnect', (reason) => {
      // Only log unexpected disconnections
      if (reason !== 'io client disconnect') {
        console.warn('Socket disconnected unexpectedly:', reason);
      }
    });

    this.socket.on('error', (error) => {
      console.error('Socket error:', error);
    });

    this.socket.on('subscription-confirmed', (data) => {
      // Subscription successful - no need to log in production
    });

    this.socket.on('unsubscription-confirmed', (data) => {
      // Unsubscription successful - no need to log in production
    });

    return this.socket;
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }

  subscribeToTopic(topicName, callback) {
    if (!this.socket) {
      this.connect();
    }
    
    // Subscribe to topic messages
    this.socket.emit('subscribe-topic', topicName);
    
    // Listen for messages
    this.socket.on('message', callback);
    
    // Store listener for cleanup
    this.listeners.set(topicName, callback);
  }

  unsubscribeFromTopic(topicName) {
    if (!this.socket) {
      return;
    }
    
    // Unsubscribe from topic
    this.socket.emit('unsubscribe-topic', topicName);
    
    // Remove listener
    const callback = this.listeners.get(topicName);
    if (callback) {
      this.socket.off('message', callback);
      this.listeners.delete(topicName);
    }
  }

  isConnected() {
    return this.socket?.connected || false;
  }
}

const socketService = new SocketService();
export default socketService; 