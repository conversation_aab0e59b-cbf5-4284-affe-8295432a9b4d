import React, { useState, useEffect } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Box, CircularProgress, useTheme, useMediaQuery } from '@mui/material';
import Navbar from './components/Layout/Navbar';
import Sidebar from './components/Layout/Sidebar';
import ProtectedRoute from './components/ProtectedRoute';
import FirstTimePasswordDialog from './components/FirstTimePasswordDialog';
import { EnvironmentProvider } from './contexts/EnvironmentContext';
import { PermissionsProvider, usePermissions } from './contexts/PermissionsContext';
import LoginWrapper from './components/LoginWrapper';
import Dashboard from './pages/Dashboard';
import Topics from './pages/Topics';
import TopicDetail from './pages/TopicDetail';
import ConsumerGroups from './pages/ConsumerGroups';
import ConsumerGroupDetail from './pages/ConsumerGroupDetail';
import MessageBrowser from './pages/MessageBrowser';
import Producer from './pages/Producer';
import ClusterInfo from './pages/ClusterInfo';
import Settings from './pages/Settings';
import UserManagement from './pages/UserManagement';

const drawerWidth = 240;

// Component to handle role-based routing
const RoleBasedRoutes = ({ isAuthenticated, user }) => {
  const { permissions } = usePermissions();
  
  // Determine default route based on user role
  const getDefaultRoute = () => {
    if (!permissions) return '/topics'; // Default to topics while loading
    
    // Super admin can access dashboard
    if (permissions.role === 'SUPER_ADMIN') {
      return '/';
    }
    
    // Topic managers and viewers go to topics page
    return '/topics';
  };

  return (
    <Routes>
      <Route 
        path="/" 
        element={
          <ProtectedRoute isAuthenticated={isAuthenticated}>
            {permissions?.role === 'SUPER_ADMIN' ? (
              <Dashboard />
            ) : (
              <Navigate to="/topics" replace />
            )}
          </ProtectedRoute>
        } 
      />
      <Route 
        path="/topics" 
        element={
          <ProtectedRoute isAuthenticated={isAuthenticated}>
            <Topics />
          </ProtectedRoute>
        } 
      />
      <Route 
        path="/topics/:topicName" 
        element={
          <ProtectedRoute isAuthenticated={isAuthenticated}>
            <TopicDetail />
          </ProtectedRoute>
        } 
      />
      <Route 
        path="/consumer-groups" 
        element={
          <ProtectedRoute isAuthenticated={isAuthenticated}>
            <ConsumerGroups />
          </ProtectedRoute>
        } 
      />
      <Route 
        path="/consumer-groups/:groupId" 
        element={
          <ProtectedRoute isAuthenticated={isAuthenticated}>
            <ConsumerGroupDetail />
          </ProtectedRoute>
        } 
      />
      <Route 
        path="/messages" 
        element={
          <ProtectedRoute isAuthenticated={isAuthenticated}>
            <MessageBrowser />
          </ProtectedRoute>
        } 
      />
      <Route 
        path="/producer" 
        element={
          <ProtectedRoute isAuthenticated={isAuthenticated}>
            <Producer />
          </ProtectedRoute>
        } 
      />
      <Route 
        path="/cluster-info" 
        element={
          <ProtectedRoute isAuthenticated={isAuthenticated}>
            <ClusterInfo />
          </ProtectedRoute>
        } 
      />
      <Route 
        path="/settings" 
        element={
          <ProtectedRoute isAuthenticated={isAuthenticated}>
            <Settings />
          </ProtectedRoute>
        } 
      />
      <Route 
        path="/user-management" 
        element={
          <ProtectedRoute isAuthenticated={isAuthenticated}>
            {permissions?.role === 'SUPER_ADMIN' ? (
              <UserManagement />
            ) : (
              <Navigate to="/topics" replace />
            )}
          </ProtectedRoute>
        } 
      />
      <Route 
        path="*" 
        element={<Navigate to={getDefaultRoute()} replace />} 
      />
    </Routes>
  );
};

function App() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [mobileOpen, setMobileOpen] = useState(false);
  const [showFirstTimePasswordDialog, setShowFirstTimePasswordDialog] = useState(false);
  
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Check if user is already logged in on app load
  useEffect(() => {
    const token = localStorage.getItem('kafka_dashboard_auth_token');
    const userData = localStorage.getItem('kafka_dashboard_user');

    if (token && userData) {
      try {
        const parsedUser = JSON.parse(userData);
        setUser(parsedUser);
        setIsAuthenticated(true);
        
        // Check if user needs to change password on first login
        if (parsedUser.requiresPasswordChange) {
          setShowFirstTimePasswordDialog(true);
        }
        
        // Dispatch login event for already authenticated users
        window.dispatchEvent(new CustomEvent('user-login', { detail: { userData: parsedUser, token } }));
      } catch (error) {
        console.error('App.js - error parsing user data:', error);
        // Invalid user data, clear storage
        localStorage.removeItem('kafka_dashboard_auth_token');
        localStorage.removeItem('kafka_dashboard_user');
      }
    }

    setIsLoading(false);
  }, []);

  const handleLogin = (userData, token) => {
    setUser(userData);
    setIsAuthenticated(true);
    localStorage.setItem('kafka_dashboard_auth_token', token);
    localStorage.setItem('kafka_dashboard_user', JSON.stringify(userData));
    
    // Check if user needs to change password on first login
    if (userData.requiresPasswordChange) {
      setShowFirstTimePasswordDialog(true);
    }
    
    // Dispatch login event
    window.dispatchEvent(new CustomEvent('user-login', { detail: { userData, token } }));
  };

  const handleLogout = () => {
    setUser(null);
    setIsAuthenticated(false);
    setShowFirstTimePasswordDialog(false);
    localStorage.removeItem('kafka_dashboard_auth_token');
    localStorage.removeItem('kafka_dashboard_user');
  };

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleFirstTimePasswordSuccess = () => {
    setShowFirstTimePasswordDialog(false);
    // Update user data to reflect that password change is no longer required
    const updatedUser = { ...user, requiresPasswordChange: false };
    setUser(updatedUser);
    localStorage.setItem('kafka_dashboard_user', JSON.stringify(updatedUser));
  };

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          flexDirection: 'column',
          gap: 2,
        }}
      >
        <CircularProgress size={60} />
        <Box sx={{ textAlign: 'center' }}>
          Loading Kafka Dashboard...
        </Box>
      </Box>
    );
  }

  // Always render PermissionsProvider, but conditionally render content
  return (
    <EnvironmentProvider>
      <PermissionsProvider>
        {!isAuthenticated ? (
          <Routes>
            <Route
              path="/login"
              element={<LoginWrapper onLogin={handleLogin} />}
            />
            <Route
              path="*"
              element={<Navigate to="/login" replace />}
            />
          </Routes>
        ) : (
        <Box sx={{ display: 'flex' }}>
        <Navbar
          user={user}
          onLogout={handleLogout}
          onDrawerToggle={handleDrawerToggle}
          isMobile={isMobile}
        />
        <Sidebar
          drawerWidth={drawerWidth}
          mobileOpen={mobileOpen}
          onDrawerToggle={handleDrawerToggle}
          isMobile={isMobile}
        />
        <Box
          component="main"
          sx={{
            flexGrow: 1,
            p: { xs: 2, sm: 3 },
            width: {
              xs: '100%',
              md: `calc(100% - ${drawerWidth}px)`
            },
            ml: {
              xs: 0,
              md: `${drawerWidth}px`
            },
            mt: { xs: 7, sm: 8 },
            minHeight: `calc(100vh - ${isMobile ? 56 : 64}px)`,
            maxHeight: `calc(100vh - ${isMobile ? 56 : 64}px)`,
            overflow: 'auto',
          }}
        >
        <RoleBasedRoutes isAuthenticated={isAuthenticated} user={user} />
      </Box>
    </Box>
        )}
        
        {/* First-time password change dialog */}
        <FirstTimePasswordDialog
          open={showFirstTimePasswordDialog}
          onSuccess={handleFirstTimePasswordSuccess}
        />
      </PermissionsProvider>
    </EnvironmentProvider>
  );
}

export default App; 