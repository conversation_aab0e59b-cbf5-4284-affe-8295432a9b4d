import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import rbacApi from '../services/rbacApi';

const PermissionsContext = createContext();

export const usePermissions = () => {
  const context = useContext(PermissionsContext);
  if (!context) {
    throw new Error('usePermissions must be used within a PermissionsProvider');
  }
  return context;
};

export const PermissionsProvider = ({ children }) => {
  const [permissions, setPermissions] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Check if user is authenticated by checking localStorage
  const isAuthenticated = useCallback(() => {
    const token = localStorage.getItem('kafka_dashboard_auth_token');
    const user = localStorage.getItem('kafka_dashboard_user');
    return !!(token && user);
  }, []);

  // Fetch user permissions
  const fetchPermissions = useCallback(async () => {
    if (!isAuthenticated()) {
      setPermissions(null);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const response = await rbacApi.getUserPermissions();
      setPermissions(response.data);
    } catch (error) {
      setError(error.message || 'Failed to fetch permissions');
      setPermissions(null);
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated]);

  // Check if user has specific permission
  const hasPermission = useCallback((permission) => {
    if (!permissions) return false;
    return permissions.permissions.includes(permission);
  }, [permissions]);

  // Check if user has access to specific topic
  const hasTopicAccess = useCallback((topicName) => {
    if (!permissions) return false;
    
    // Super admin has access to all topics
    if (permissions.role === 'SUPER_ADMIN') return true;
    
    // User with all topics access
    if (permissions.hasAllTopicsAccess) return true;
    
    // Check if topic is in assigned topics
    return permissions.assignedTopics.includes(topicName);
  }, [permissions]);

  // Check if user is super admin
  const isSuperAdmin = useCallback(() => {
    return permissions?.role === 'SUPER_ADMIN';
  }, [permissions]);

  // Check if user is topic manager
  const isTopicManager = useCallback(() => {
    return permissions?.role === 'TOPIC_MANAGER';
  }, [permissions]);

  // Check if user is topic viewer
  const isTopicViewer = useCallback(() => {
    return permissions?.role === 'TOPIC_VIEWER';
  }, [permissions]);

  // Get user's accessible topics from a list of all topics
  const getAccessibleTopics = useCallback((allTopics) => {
    if (!permissions || !allTopics) return [];
    
    // Super admin has access to all topics
    if (permissions.role === 'SUPER_ADMIN') return allTopics;
    
    // User with all topics access
    if (permissions.hasAllTopicsAccess) return allTopics;
    
    // Filter topics based on assigned topics
    return allTopics.filter(topic => 
      permissions.assignedTopics.includes(topic.name || topic)
    );
  }, [permissions]);

  // Refresh permissions (useful after role changes)
  const refreshPermissions = useCallback(() => {
    fetchPermissions();
  }, [fetchPermissions]);

  useEffect(() => {
    const checkAuthAndFetch = () => {
      const token = localStorage.getItem('kafka_dashboard_auth_token');
      const user = localStorage.getItem('kafka_dashboard_user');
      
      if (token && user) {
        fetchPermissions();
      } else {
        setPermissions(null);
        setLoading(false);
      }
    };

    // Check immediately
    checkAuthAndFetch();

    // Listen for storage changes (login/logout in other tabs)
    const handleStorageChange = (e) => {
      if (e.key === 'kafka_dashboard_auth_token' || e.key === 'kafka_dashboard_user') {
        checkAuthAndFetch();
      }
    };

    // Listen for custom login event
    const handleLogin = () => {
      setTimeout(checkAuthAndFetch, 100); // Small delay to ensure token is set
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('user-login', handleLogin);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('user-login', handleLogin);
    };
  }, [fetchPermissions]); // Add fetchPermissions to dependencies

  const value = {
    permissions,
    loading,
    error,
    hasPermission,
    hasTopicAccess,
    isSuperAdmin,
    isTopicManager,
    isTopicViewer,
    getAccessibleTopics,
    refreshPermissions
  };

  return (
    <PermissionsContext.Provider value={value}>
      {children}
    </PermissionsContext.Provider>
  );
};

// Permission constants (should match backend)
export const PERMISSIONS = {
  // Dashboard permissions
  VIEW_DASHBOARD: 'view_dashboard',
  
  // Cluster permissions
  VIEW_CLUSTER_INFO: 'view_cluster_info',
  
  // User management permissions
  MANAGE_USERS: 'manage_users',
  VIEW_USERS: 'view_users',
  CREATE_USERS: 'create_users',
  UPDATE_USERS: 'update_users',
  DELETE_USERS: 'delete_users',
  
  // Topic permissions
  VIEW_ALL_TOPICS: 'view_all_topics',
  VIEW_ASSIGNED_TOPICS: 'view_assigned_topics',
  CREATE_TOPICS: 'create_topics',
  UPDATE_TOPICS: 'update_topics',
  DELETE_TOPICS: 'delete_topics',
  MANAGE_TOPIC_PARTITIONS: 'manage_topic_partitions',
  
  // Message permissions
  VIEW_MESSAGES: 'view_messages',
  PRODUCE_MESSAGES: 'produce_messages',
  
  // Consumer group permissions
  VIEW_ALL_CONSUMER_GROUPS: 'view_all_consumer_groups',
  VIEW_ASSIGNED_CONSUMER_GROUPS: 'view_assigned_consumer_groups',
  DELETE_CONSUMER_GROUPS: 'delete_consumer_groups',
  
  // Environment permissions
  SWITCH_ENVIRONMENT: 'switch_environment'
};
