import React, { createContext, useContext, useState, useEffect } from 'react';
import { environmentApi } from '../services/api';
import toast from 'react-hot-toast';
import { useQueryClient } from 'react-query';

const EnvironmentContext = createContext();

export const useEnvironment = () => {
  const context = useContext(EnvironmentContext);
  if (!context) {
    throw new Error('useEnvironment must be used within an EnvironmentProvider');
  }
  return context;
};

export const EnvironmentProvider = ({ children }) => {
  const [currentEnvironment, setCurrentEnvironment] = useState(null);
  const [availableEnvironments, setAvailableEnvironments] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSwitching, setIsSwitching] = useState(false);
  const queryClient = useQueryClient();

  // Load initial environment data
  useEffect(() => {
    loadEnvironmentData();
  }, []);

  const loadEnvironmentData = async () => {
    // Don't load environment data if user is not authenticated
    const token = localStorage.getItem('kafka_dashboard_auth_token');
    if (!token) {
      // Set default values without API call
      setAvailableEnvironments([
        { key: 'qa', name: 'QA Environment', description: 'Quality Assurance - Single Broker', brokerCount: 1 },
        { key: 'prod', name: 'Production Environment', description: 'Production - Multiple Brokers', brokerCount: 3 }
      ]);
      setCurrentEnvironment({ key: 'prod', name: 'Production Environment' });
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      
      // Load available environments and current environment in parallel
      const [availableResponse, currentResponse] = await Promise.all([
        environmentApi.getAvailableEnvironments(),
        environmentApi.getCurrentEnvironment()
      ]);

      setAvailableEnvironments(availableResponse.data);
      setCurrentEnvironment(currentResponse.data);
    } catch (error) {
      console.error('Error loading environment data:', error);
      // Only show toast if user is authenticated
      if (token) {
        toast.error('Failed to load environment information');
      }
      
      // Set default values if API fails
      setAvailableEnvironments([
        { key: 'qa', name: 'QA Environment', description: 'Quality Assurance - Single Broker', brokerCount: 1 },
        { key: 'prod', name: 'Production Environment', description: 'Production - Multiple Brokers', brokerCount: 3 }
      ]);
      setCurrentEnvironment({ key: 'prod', name: 'Production Environment' });
    } finally {
      setIsLoading(false);
    }
  };

  const switchEnvironment = async (environmentKey) => {
    if (isSwitching || currentEnvironment?.key === environmentKey) {
      return;
    }

    try {
      setIsSwitching(true);

      const response = await environmentApi.switchEnvironment(environmentKey);
      setCurrentEnvironment(response.data);

      toast.success(`Switched to ${response.data.name}`);
      
      // Add a small delay to ensure backend has switched before invalidating queries
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Invalidate all queries to force fresh data fetch from new environment
      queryClient.invalidateQueries();
      
      // Reload environment data
      await loadEnvironmentData();
      
    } catch (error) {
      console.error('Error switching environment:', error);
      const errorMessage = error.response?.data?.message || error.message || 'Failed to switch environment';
      toast.error(errorMessage);
    } finally {
      setIsSwitching(false);
    }
  };

  const testConnection = async (environmentKey) => {
    try {
      const response = await environmentApi.testConnection(environmentKey);
      
      if (response.success) {
        toast.success(`Connection to ${environmentKey.toUpperCase()} successful`);
        return response;
      } else {
        toast.error(`Connection to ${environmentKey.toUpperCase()} failed`);
        return null;
      }
    } catch (error) {
      console.error('Error testing connection:', error);
      toast.error(`Connection test failed: ${error.message}`);
      return null;
    }
  };

  const refreshEnvironmentData = async () => {
    await loadEnvironmentData();
  };

  const value = {
    currentEnvironment,
    availableEnvironments,
    isLoading,
    isSwitching,
    switchEnvironment,
    testConnection,
    refreshEnvironmentData
  };

  return (
    <EnvironmentContext.Provider value={value}>
      {children}
    </EnvironmentContext.Provider>
  );
};
