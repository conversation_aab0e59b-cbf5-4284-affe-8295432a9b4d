import { useQuery } from 'react-query';
import { clusterApi } from '../services/api';

/**
 * Shared hook for fetching cluster info data
 * This ensures all components use the same query configuration
 * and prevents duplicate API calls
 */
export const useClusterInfo = (options = {}) => {
  return useQuery(
    'cluster-info',
    clusterApi.getInfo,
    {
      // Default configuration that all components will share
      refetchInterval: 60000, // 60 seconds
      retry: 3,
      retryDelay: 1000,
      staleTime: 0, // Always consider data stale to force refetch
      // Allow components to override specific options
      ...options
    }
  );
}; 