import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
  CircularProgress,
  InputAdornment,
  Tooltip,
  useTheme,
  useMediaQuery,
  Stack,
} from '@mui/material';
import {
  PersonAdd,
  Edit,
  Delete,
  Search,
  Clear,
  Person,
  AdminPanelSettings,
  Block,
  CheckCircle,
  Refresh,
  Lock,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import toast from 'react-hot-toast';
import { authApi, topicsApi } from '../services/api';
import rbacApi from '../services/rbacApi';
import { useDebounce } from '../hooks/useDebounce';
import { usePermissions } from '../contexts/PermissionsContext';
import { FixedSizeList as List } from 'react-window';
import UserFormDialog from '../components/UserManagement/UserFormDialog';
import ResetPasswordDialog from '../components/ResetPasswordDialog';

const UserCard = ({ user, onEdit, onDelete, onToggleStatus, onResetPassword, currentUserId }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const getRoleColor = (role) => {
    switch (role) {
      case 'SUPER_ADMIN':
        return 'error';
      case 'TOPIC_MANAGER':
        return 'warning';
      case 'TOPIC_VIEWER':
        return 'primary';
      default:
        return 'default';
    }
  };

  const getRoleIcon = (role) => {
    switch (role) {
      case 'SUPER_ADMIN':
        return <AdminPanelSettings fontSize="small" />;
      case 'TOPIC_MANAGER':
        return <Edit fontSize="small" />;
      case 'TOPIC_VIEWER':
        return <Person fontSize="small" />;
      default:
        return <Person fontSize="small" />;
    }
  };

  const getRoleDisplayName = (role) => {
    switch (role) {
      case 'SUPER_ADMIN':
        return 'Super Admin';
      case 'TOPIC_MANAGER':
        return 'Topic Manager';
      case 'TOPIC_VIEWER':
        return 'Topic Viewer';
      default:
        return role;
    }
  };

  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const isCurrentUser = user._id === currentUserId;

  return (
    <Card sx={{ 
      mb: 2,
      '&:hover': {
        boxShadow: theme.shadows[4],
        transform: 'translateY(-1px)',
        transition: 'all 0.2s ease-in-out'
      }
    }}>
      <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
        <Box sx={{ 
          display: 'flex', 
          flexDirection: { xs: 'column', sm: 'row' },
          justifyContent: 'space-between', 
          alignItems: { xs: 'stretch', sm: 'flex-start' },
          gap: { xs: 2, sm: 0 }
        }}>
          <Box sx={{ flexGrow: 1, minWidth: 0 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Person sx={{ mr: 1, color: 'primary.main' }} />
              <Typography 
                variant={isMobile ? "subtitle1" : "h6"} 
                component="h2" 
                sx={{ 
                  fontWeight: 600, 
                  wordBreak: 'break-word',
                  fontSize: { xs: '1rem', sm: '1.25rem' }
                }}
              >
                {user.username}
                {isCurrentUser && (
                  <Chip 
                    label="You" 
                    size="small" 
                    color="info" 
                    sx={{ ml: 1, fontSize: '0.7rem' }}
                  />
                )}
              </Typography>
            </Box>
            
            <Typography 
              variant="body2" 
              color="text.secondary" 
              sx={{ mb: 1, fontSize: { xs: '0.8rem', sm: '0.875rem' } }}
            >
              {user.email}
            </Typography>

            <Stack 
              direction={isMobile ? "column" : "row"} 
              spacing={1} 
              alignItems={isMobile ? "flex-start" : "center"} 
              flexWrap="wrap" 
              mt={1}
              sx={{ gap: { xs: 0.5, sm: 1 } }}
            >
              <Chip
                icon={getRoleIcon(user.role)}
                label={getRoleDisplayName(user.role)}
                size="small"
                color={getRoleColor(user.role)}
                variant="outlined"
                sx={{ fontSize: { xs: '0.7rem', sm: '0.75rem' } }}
              />

              {/* Show topic access info for non-super-admin users */}
              {user.role !== 'SUPER_ADMIN' && (
                <Chip
                  label={user.hasAllTopicsAccess ? 'All Topics' : `${user.assignedTopics?.length || 0} Topics`}
                  size="small"
                  color="info"
                  variant="outlined"
                  sx={{ fontSize: { xs: '0.7rem', sm: '0.75rem' } }}
                />
              )}
              <Chip
                icon={user.isActive ? <CheckCircle fontSize="small" /> : <Block fontSize="small" />}
                label={user.isActive ? 'Active' : 'Inactive'}
                size="small"
                color={user.isActive ? 'success' : 'error'}
                variant="outlined"
                sx={{ fontSize: { xs: '0.7rem', sm: '0.75rem' } }}
              />
              {user.lastLogin && (
                <Chip
                  label={`Last login: ${formatDate(user.lastLogin)}`}
                  size="small"
                  variant="outlined"
                  sx={{ fontSize: { xs: '0.7rem', sm: '0.75rem' } }}
                />
              )}
            </Stack>
          </Box>

          <Stack 
            direction="row" 
            spacing={0.5}
            sx={{ 
              alignSelf: { xs: 'flex-end', sm: 'center' },
              mt: { xs: 1, sm: 0 }
            }}
          >
            <Tooltip title="Edit User">
              <IconButton 
                size="small" 
                onClick={() => onEdit(user)}
                sx={{ p: { xs: 0.5, sm: 1 } }}
              >
                <Edit fontSize="small" />
              </IconButton>
            </Tooltip>
            
            <Tooltip title={user.isActive ? "Deactivate User" : "Activate User"}>
              <IconButton 
                size="small" 
                onClick={() => onToggleStatus(user)}
                color={user.isActive ? "warning" : "success"}
                sx={{ p: { xs: 0.5, sm: 1 } }}
              >
                {user.isActive ? <Block fontSize="small" /> : <CheckCircle fontSize="small" />}
              </IconButton>
            </Tooltip>
            
            <Tooltip title="Reset Password">
              <IconButton 
                size="small" 
                onClick={() => onResetPassword(user)}
                color="info"
                sx={{ p: { xs: 0.5, sm: 1 } }}
              >
                <Lock fontSize="small" />
              </IconButton>
            </Tooltip>
            
            {!isCurrentUser && (
              <Tooltip title="Delete User">
                <IconButton 
                  size="small" 
                  onClick={() => onDelete(user)}
                  color="error"
                  sx={{ p: { xs: 0.5, sm: 1 } }}
                >
                  <Delete fontSize="small" />
                </IconButton>
              </Tooltip>
            )}
          </Stack>
        </Box>
      </CardContent>
    </Card>
  );
};

const UserManagement = () => {
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [resetPasswordOpen, setResetPasswordOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');

  const queryClient = useQueryClient();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { isSuperAdmin, loading: permissionsLoading } = usePermissions();

  // Debounce search term
  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  // Get current user
  const { data: currentUser } = useQuery('user-profile', async () => {
    const response = await authApi.getProfile();
    return response.data;
  });

  // Get all users
  const { data: users, isLoading } = useQuery('users', async () => {
    const response = await authApi.getUsers();
    return response.data;
  }, {
    refetchInterval: 120000, // Reduced from 30s to 120s since user data changes less frequently
  });

  // Get all roles
  const { data: roles } = useQuery('roles', async () => {
    const response = await rbacApi.getRoles();
    return response.data;
  }, {
    enabled: isSuperAdmin(),
  });

  // Get all topics for topic assignment
  const { data: topics } = useQuery('topics', async () => {
    const response = await topicsApi.getAll();
    return response.data;
  }, {
    enabled: isSuperAdmin(),
  });

  // Filter users based on search term
  const filteredUsers = React.useMemo(() => {
    if (!users || !debouncedSearchTerm.trim()) {
      return users || [];
    }

    return users.filter(user =>
      user.username.toLowerCase().includes(debouncedSearchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(debouncedSearchTerm.toLowerCase())
    );
  }, [users, debouncedSearchTerm]);

  const [generatedPassword, setGeneratedPassword] = useState(null);
  const [assignedTopics, setAssignedTopics] = useState([]);

  // Create user mutation
  const createUserMutation = useMutation(rbacApi.registerUser, {
    onSuccess: (response) => {
      const { generatedPassword: newPassword } = response.data;
      if (newPassword) {
        setGeneratedPassword(newPassword);
        // Copy password to clipboard
        navigator.clipboard.writeText(newPassword).then(() => {
          toast.success('User created successfully! Password copied to clipboard.');
        }).catch(() => {
          toast.success('User created successfully! Please copy the password manually.');
        });
      } else {
        toast.success('User created successfully');
        setCreateDialogOpen(false);
      }
      queryClient.invalidateQueries('users');
    },
    onError: (error) => {
      toast.error(error.response?.data?.message || 'Failed to create user');
    },
  });

  // Update user role mutation
  const updateUserRoleMutation = useMutation(
    ({ userId, roleData }) => rbacApi.updateUserRole(userId, roleData),
    {
      onSuccess: () => {
        toast.success('User role and permissions updated successfully');
        queryClient.invalidateQueries('users');
        setEditDialogOpen(false);
        setSelectedUser(null);
      },
      onError: (error) => {
        toast.error(error.response?.data?.message || 'Failed to update user role');
      },
    }
  );

  // Delete user mutation
  const deleteUserMutation = useMutation(authApi.deleteUser, {
    onSuccess: () => {
      toast.success('User deleted successfully');
      queryClient.invalidateQueries('users');
      setDeleteConfirmOpen(false);
      setSelectedUser(null);
    },
    onError: (error) => {
      toast.error(error.response?.data?.message || 'Failed to delete user');
    },
  });

  // Reset password mutation
  const resetPasswordMutation = useMutation(
    ({ userId, passwordData }) => authApi.resetPassword(userId, passwordData),
    {
      onSuccess: () => {
        toast.success('Password reset successfully. User can login with the new password and will be prompted to change it.');
        queryClient.invalidateQueries('users');
        setResetPasswordOpen(false);
        setSelectedUser(null);
      },
      onError: (error) => {
        toast.error(error.response?.data?.message || 'Failed to reset password');
      },
    }
  );

  const handleCreateUser = (userData) => {
    // Store assigned topics for display in password dialog
    setAssignedTopics(userData.assignedTopics || []);
    createUserMutation.mutate(userData);
  };

  const handleEditUser = (user) => {
    setSelectedUser(user);
    setEditDialogOpen(true);
  };

  const handleUpdateUser = (roleData) => {
    if (!selectedUser) return;
    updateUserRoleMutation.mutate({ userId: selectedUser._id, roleData });
  };

  const handleDeleteUser = (user) => {
    setSelectedUser(user);
    setDeleteConfirmOpen(true);
  };

  const confirmDelete = () => {
    if (selectedUser) {
      deleteUserMutation.mutate(selectedUser._id);
    }
  };

  const handleToggleStatus = (user) => {
    // Using authApi.updateUser for status changes
    authApi.updateUser(user._id, { isActive: !user.isActive })
      .then(() => {
        toast.success('User status updated successfully');
        queryClient.invalidateQueries('users');
      })
      .catch((error) => {
        toast.error(error.message || 'Failed to update user status');
      });
  };

  const handleResetPassword = (user) => {
    setSelectedUser(user);
    setResetPasswordOpen(true);
  };

  const confirmResetPassword = (newPassword, generateRandom = true) => {
    if (selectedUser) {
      resetPasswordMutation.mutate({ 
        userId: selectedUser._id, 
        passwordData: { 
          newPassword,
          generateRandom 
        } 
      });
    }
  };

  // Only super admins can access user management
  if (permissionsLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (!isSuperAdmin()) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          Access denied. Only Super Administrators can access User Management.
        </Alert>
      </Box>
    );
  }

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Box sx={{
        display: 'flex',
        flexDirection: { xs: 'column', sm: 'row' },
        justifyContent: 'space-between',
        alignItems: { xs: 'stretch', sm: 'center' },
        mb: { xs: 2, sm: 4 },
        gap: { xs: 2, sm: 0 }
      }}>
        <Typography
          variant={isMobile ? "h5" : "h4"}
          sx={{
            fontSize: { xs: '1.5rem', sm: '2.125rem' },
            fontWeight: 600,
          }}
        >
          User Management
        </Typography>
        <Stack direction={isMobile ? "column" : "row"} spacing={1}>
          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={() => queryClient.invalidateQueries('users')}
            size={isMobile ? "small" : "medium"}
            fullWidth={isMobile}
          >
            Refresh
          </Button>
          <Button
            variant="contained"
            startIcon={<PersonAdd />}
            onClick={() => setCreateDialogOpen(true)}
            size={isMobile ? "small" : "medium"}
            fullWidth={isMobile}
          >
            Create User
          </Button>
        </Stack>
      </Box>

      {/* Search Bar */}
      <Box sx={{ mb: 3 }}>
        <TextField
          fullWidth
          placeholder="Search users by username or email..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Search />
              </InputAdornment>
            ),
            endAdornment: searchTerm && (
              <InputAdornment position="end">
                <IconButton
                  aria-label="clear search"
                  onClick={() => setSearchTerm('')}
                  edge="end"
                  size="small"
                >
                  <Clear />
                </IconButton>
              </InputAdornment>
            ),
          }}
          variant="outlined"
          size={isMobile ? "small" : "medium"}
        />
      </Box>

      {/* Users List */}
      {users?.length === 0 ? (
        <Alert severity="info" sx={{ mb: 2 }}>
          No users found.
        </Alert>
      ) : filteredUsers.length === 0 && debouncedSearchTerm ? (
        <Alert severity="info" sx={{ mb: 2 }}>
          No users found matching "{debouncedSearchTerm}". Try a different search term.
        </Alert>
      ) : (
        <List
          height={Math.min(900, window.innerHeight - 300)}
          itemCount={filteredUsers.length}
          itemSize={isMobile ? 200 : 160}
          width="100%"
        >
          {({ index, style }) => {
            const user = filteredUsers[index];
            return (
              <div style={style} key={user._id}>
                <UserCard
                  user={user}
                  onEdit={handleEditUser}
                  onDelete={handleDeleteUser}
                  onToggleStatus={handleToggleStatus}
                  onResetPassword={handleResetPassword}
                  currentUserId={currentUser?.data?._id}
                />
              </div>
            );
          }}
        </List>
      )}

      {/* Dialogs */}
      <UserFormDialog
        open={createDialogOpen}
        onClose={() => {
          setCreateDialogOpen(false);
          setGeneratedPassword(null);
          setAssignedTopics([]);
        }}
        onSubmit={handleCreateUser}
        roles={roles}
        topics={topics}
        isLoading={createUserMutation.isLoading}
        title="Create New User"
        generatedPassword={generatedPassword}
        assignedTopics={assignedTopics}
      />

      <UserFormDialog
        open={editDialogOpen}
        user={selectedUser}
        onClose={() => {
          setEditDialogOpen(false);
          setSelectedUser(null);
        }}
        onSubmit={handleUpdateUser}
        roles={roles}
        topics={topics}
        isLoading={updateUserRoleMutation.isLoading}
        title="Edit User"
      />

      <Dialog
        open={deleteConfirmOpen}
        onClose={() => setDeleteConfirmOpen(false)}
      >
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete user "{selectedUser?.username}"? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteConfirmOpen(false)}>Cancel</Button>
          <Button
            onClick={confirmDelete}
            color="error"
            variant="contained"
            disabled={deleteUserMutation.isLoading}
          >
            {deleteUserMutation.isLoading ? 'Deleting...' : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>

      <ResetPasswordDialog
        open={resetPasswordOpen}
        onClose={() => {
          setResetPasswordOpen(false);
          setSelectedUser(null);
        }}
        onSubmit={confirmResetPassword}
        user={selectedUser}
        isLoading={resetPasswordMutation.isLoading}
      />
    </Box>
  );
};

export default UserManagement;
