import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Alert,
  CircularProgress,
  Divider,
} from '@mui/material';
import { ArrowBack } from '@mui/icons-material';
import { useQuery } from 'react-query';
import { consumerGroupsApi } from '../services/api';

const ConsumerGroupDetail = () => {
  const { groupId } = useParams();
  const navigate = useNavigate();

  const { data: groupDetails, isLoading, error } = useQuery(
    ['consumer-group', groupId],
    () => consumerGroupsApi.getById(groupId),
    { enabled: !!groupId }
  );

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        Error loading consumer group: {error.message}
      </Alert>
    );
  }

  if (!groupDetails) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        Consumer group not found
      </Alert>
    );
  }

  const group = groupDetails;

  // Helper function to get total members count
  const getMembersCount = () => {
    return Array.isArray(group.members) ? group.members.length : 0;
  };

  // Helper function to get coordinator info
  const getCoordinatorInfo = () => {
    if (!group.coordinator) return 'Not available';
    const { host, port } = group.coordinator;
    if (host === 'N/A' || port === 'N/A') return 'Not available';
    return `${host}:${port}`;
  };

  // Helper function to flatten offsets for display
  const getFlattenedOffsets = () => {
    if (!Array.isArray(group.offsets)) return [];
    
    const flattened = [];
    group.offsets.forEach(topicOffset => {
      if (topicOffset.partitions && Array.isArray(topicOffset.partitions)) {
        topicOffset.partitions.forEach(partition => {
          flattened.push({
            topic: topicOffset.topic,
            partition: partition.partition,
            offset: partition.offset,
            metadata: partition.metadata
          });
        });
      }
    });
    return flattened;
  };

  // Helper function to get active members count
  const getActiveMembersCount = () => {
    if (!Array.isArray(group.members)) return 0;
    return group.members.filter(member => 
      member.memberId && member.memberId !== 'N/A'
    ).length;
  };

  const flattenedOffsets = getFlattenedOffsets();
  const activeMembersCount = getActiveMembersCount();

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <IconButton onClick={() => navigate('/consumer-groups')} sx={{ mr: 2 }}>
          <ArrowBack />
        </IconButton>
        <Typography variant="h4">{groupId}</Typography>
      </Box>

      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Group Information
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1.5 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body2" color="textSecondary">
                    Group ID:
                  </Typography>
                  <Typography variant="body2" sx={{ fontWeight: 500 }}>
                    {group.groupId}
                  </Typography>
                </Box>
                
                <Divider />
                
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body2" color="textSecondary">
                    State:
                  </Typography>
                  <Chip
                    label={group.state || 'Unknown'}
                    color={group.state === 'Stable' ? 'success' : group.state === 'Empty' ? 'warning' : 'default'}
                    size="small"
                  />
                </Box>
                
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body2" color="textSecondary">
                    Protocol Type:
                  </Typography>
                  <Typography variant="body2">{group.protocolType || 'N/A'}</Typography>
                </Box>
                
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body2" color="textSecondary">
                    Protocol:
                  </Typography>
                  <Typography variant="body2">{group.protocol || 'N/A'}</Typography>
                </Box>
                
                <Divider />
                
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body2" color="textSecondary">
                    Total Members:
                  </Typography>
                  <Typography variant="body2" sx={{ fontWeight: 500 }}>
                    {getMembersCount()}
                  </Typography>
                </Box>
                
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body2" color="textSecondary">
                    Active Members:
                  </Typography>
                  <Typography variant="body2" sx={{ fontWeight: 500 }}>
                    {activeMembersCount}
                  </Typography>
                </Box>
                
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body2" color="textSecondary">
                    Coordinator:
                  </Typography>
                  <Typography variant="body2">{getCoordinatorInfo()}</Typography>
                </Box>
                
                {group.topics && group.topics.length > 0 && (
                  <>
                    <Divider />
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                      <Typography variant="body2" color="textSecondary">
                        Consuming Topics:
                      </Typography>
                      <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap', maxWidth: '60%' }}>
                        {group.topics.map((topic, index) => (
                          <Chip key={index} label={topic} size="small" color="primary" variant="outlined" />
                        ))}
                      </Box>
                    </Box>
                  </>
                )}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Group Members ({getMembersCount()})
              </Typography>
              {getMembersCount() === 0 ? (
                <Alert severity="info">
                  No members in this consumer group.
                </Alert>
              ) : (
                <TableContainer component={Paper} sx={{ maxHeight: 400 }}>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>Member ID</TableCell>
                        <TableCell>Client ID</TableCell>
                        <TableCell>Host</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {group.members?.map((member, index) => (
                        <TableRow key={index}>
                          <TableCell>
                            <Typography variant="body2" sx={{ 
                              fontFamily: 'monospace', 
                              fontSize: '0.75rem',
                              color: member.memberId === 'N/A' ? 'text.secondary' : 'text.primary'
                            }}>
                              {member.memberId || 'N/A'}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2" sx={{ 
                              color: member.clientId === 'N/A' ? 'text.secondary' : 'text.primary'
                            }}>
                              {member.clientId || 'N/A'}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2" sx={{ 
                              fontFamily: 'monospace',
                              fontSize: '0.75rem',
                              color: member.clientHost === 'N/A' ? 'text.secondary' : 'text.primary'
                            }}>
                              {member.clientHost || 'N/A'}
                            </Typography>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              )}
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Topic Offsets ({flattenedOffsets.length})
              </Typography>
              {flattenedOffsets.length === 0 ? (
                <Alert severity="info">
                  No offset information available for this group.
                </Alert>
              ) : (
                <TableContainer component={Paper}>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Topic</TableCell>
                        <TableCell>Partition</TableCell>
                        <TableCell>Current Offset</TableCell>
                        <TableCell>Metadata</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {flattenedOffsets.map((offset, index) => (
                        <TableRow key={index}>
                          <TableCell>
                            <Typography variant="body2" sx={{ fontWeight: 500 }}>
                              {offset.topic}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={offset.partition}
                              size="small"
                              color="primary"
                            />
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                              {offset.offset}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                              {offset.metadata || 'N/A'}
                            </Typography>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default ConsumerGroupDetail; 