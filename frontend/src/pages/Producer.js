import React, { useState } from 'react';
import { usePermissions, PERMISSIONS } from '../contexts/PermissionsContext';
import {
  Box,
  Typography,
  Card,
  CardContent,
  TextField,
  Button,
  Grid,
  Alert,
  CircularProgress,
  Tabs,
  Tab,
  Paper,
  useTheme,
  useMediaQuery,
  Autocomplete,
  Stack,
  Chip,
  LinearProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider
} from '@mui/material';
import {
  Send,
  Clear,
  Upload,
  ContentPaste,
  Download,
  ExpandMore,
  CheckCircle,
} from '@mui/icons-material';
import { useMutation } from 'react-query';
import toast from 'react-hot-toast';
import { topicsApi } from '../services/api';
import { useTopics } from '../hooks/useTopics';

const TabPanel = ({ children, value, index, ...other }) => (
  <div
    role="tabpanel"
    hidden={value !== index}
    id={`producer-tabpanel-${index}`}
    aria-labelledby={`producer-tab-${index}`}
    {...other}
  >
    {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
  </div>
);

const Producer = () => {
  const [tabValue, setTabValue] = useState(0);
  const [selectedTopic, setSelectedTopic] = useState('');
  const [messageKey, setMessageKey] = useState('');
  const [messageValue, setMessageValue] = useState('');
  const [headers, setHeaders] = useState('{}');
  const [remainingMessages, setRemainingMessages] = useState(1000); // Daily limit tracker

  // Bulk produce states
  const [bulkMode, setBulkMode] = useState('paste'); // 'paste' or 'upload'
  const [bulkJson, setBulkJson] = useState('');
  const [bulkFile, setBulkFile] = useState(null);
  const [bulkValidation, setBulkValidation] = useState({ isValid: false, errors: [] });
  const [bulkPreview, setBulkPreview] = useState([]);
  const [batchSize, setBatchSize] = useState(10);

  const { hasPermission, getAccessibleTopics, loading: permissionsLoading } = usePermissions();
  const [messageHistory, setMessageHistory] = useState([]);

  const theme = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('sm'));

  const { data: topics, isLoading: topicsLoading } = useTopics();

  const produceMutation = useMutation(
    ({ topic, message }) => topicsApi.produceMessage(topic, message),
    {
      onSuccess: (data) => {
        toast.success('Message sent successfully');
        
        console.log('Produce message response:', data);
        
        // Update remaining messages from response body
        if (data && data.remainingMessages !== undefined) {
          console.log('Remaining count from response:', data.remainingMessages);
          setRemainingMessages(data.remainingMessages);
          console.log('Updated remaining messages to:', data.remainingMessages);
        } else {
          console.log('No remainingMessages found in response');
        }
        
        // Add to message history
        setMessageHistory(prev => [{
          id: Date.now(),
          topic: selectedTopic,
          key: messageKey,
          value: messageValue,
          timestamp: new Date().toISOString(),
          status: 'success'
        }, ...prev.slice(0, 19)]); // Keep only last 20 messages
        // Clear form
        setMessageKey('');
        setMessageValue('');
      },
      onError: (error) => {
        toast.error(`Error sending message: ${error.message}`);
        setMessageHistory(prev => [{
          id: Date.now(),
          topic: selectedTopic,
          key: messageKey,
          value: messageValue,
          timestamp: new Date().toISOString(),
          status: 'error',
          error: error.message
        }, ...prev.slice(0, 19)]);
      },
    }
  );

  const bulkProduceMutation = useMutation(
    ({ topic, messages, batchSize }) => topicsApi.produceMessagesBulk(topic, messages, batchSize),
    {
      onSuccess: (data) => {
        const { successfulMessages, failedMessages, totalMessages } = data.data || data;
        if (failedMessages === 0) {
          toast.success(`Successfully produced all ${totalMessages} messages`);
        } else if (successfulMessages === 0) {
          toast.error(`Failed to produce any messages`);
        } else {
          toast.success(`Produced ${successfulMessages} messages, ${failedMessages} failed`);
        }
        
        console.log('Bulk produce response:', data);
        
        // Update remaining messages from response body
        if (data && data.remainingMessages !== undefined) {
          console.log('Remaining count from response (bulk):', data.remainingMessages);
          setRemainingMessages(data.remainingMessages);
          console.log('Updated remaining messages to (bulk):', data.remainingMessages);
        } else {
          console.log('No remainingMessages found in bulk response');
        }
        
        // Add to message history
        setMessageHistory(prev => [{
          id: Date.now(),
          topic: selectedTopic,
          key: `Bulk: ${successfulMessages}/${totalMessages}`,
          value: `Bulk operation completed`,
          timestamp: new Date().toISOString(),
          status: successfulMessages === totalMessages ? 'success' : 'error',
          error: failedMessages > 0 ? `${failedMessages} messages failed` : null
        }, ...prev.slice(0, 19)]);
      },
      onError: (error) => {
        toast.error(`Error in bulk produce: ${error.message}`);
        setMessageHistory(prev => [{
          id: Date.now(),
          topic: selectedTopic,
          key: 'Bulk Operation',
          value: 'Bulk produce failed',
          timestamp: new Date().toISOString(),
          status: 'error',
          error: error.message
        }, ...prev.slice(0, 19)]);
      },
    }
  );

  // Filter topics based on user permissions
  const accessibleTopics = getAccessibleTopics(topics || []);

  // Check if user has permission to produce messages
  if (permissionsLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (!hasPermission(PERMISSIONS.PRODUCE_MESSAGES)) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          Access denied. You do not have permission to produce messages.
        </Alert>
      </Box>
    );
  }

  const handleSendMessage = () => {
    if (!selectedTopic) {
      toast.error('Please select a topic');
      return;
    }
    if (!messageValue.trim()) {
      toast.error('Message value is required');
      return;
    }

    let parsedHeaders = {};
    if (headers.trim()) {
      try {
        parsedHeaders = JSON.parse(headers);
      } catch (error) {
        toast.error('Invalid JSON format for headers');
        return;
      }
    }

    const message = {
      key: messageKey || null,
      value: messageValue,
      headers: parsedHeaders,
    };

    produceMutation.mutate({ topic: selectedTopic, message });
  };

  const handleClearForm = () => {
    setMessageKey('');
    setMessageValue('');
    setHeaders('{}');
  };

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // Bulk validation and preview functions
  const validateBulkJson = (jsonString) => {
    try {
      const parsed = JSON.parse(jsonString);
      if (!Array.isArray(parsed)) {
        return { isValid: false, errors: ['JSON must be an array of message objects'] };
      }
      
      if (parsed.length === 0) {
        return { isValid: false, errors: ['Array cannot be empty'] };
      }
      
      if (parsed.length > 1000) {
        return { isValid: false, errors: ['Maximum 1000 messages allowed'] };
      }
      
      const errors = [];
      const preview = [];
      
      parsed.forEach((message, index) => {
        if (!message.value || typeof message.value !== 'string') {
          errors.push(`Message ${index + 1}: value is required and must be a string`);
        }
        
        if (message.key !== undefined && typeof message.key !== 'string') {
          errors.push(`Message ${index + 1}: key must be a string`);
        }
        
        if (message.partition !== undefined && (typeof message.partition !== 'number' || message.partition < 0)) {
          errors.push(`Message ${index + 1}: partition must be a non-negative number`);
        }
        
        if (message.headers !== undefined && typeof message.headers !== 'object') {
          errors.push(`Message ${index + 1}: headers must be an object`);
        }
        
        if (errors.length <= 10) { // Limit preview to first 10 messages
          preview.push({
            key: message.key || 'null',
            value: message.value?.substring(0, 100) + (message.value?.length > 100 ? '...' : ''),
            partition: message.partition !== undefined ? message.partition : 'auto',
            headers: message.headers || {}
          });
        }
      });
      
      return {
        isValid: errors.length === 0,
        errors,
        preview: errors.length === 0 ? preview : []
      };
    } catch (error) {
      return { isValid: false, errors: ['Invalid JSON format'] };
    }
  };

  const handleBulkJsonChange = (value) => {
    setBulkJson(value);
    const validation = validateBulkJson(value);
    setBulkValidation(validation);
    setBulkPreview(validation.preview || []);
  };

  const handleFileUpload = (event) => {
    const file = event.target.files[0];
    if (!file) return;
    
    if (file.size > 5 * 1024 * 1024) { // 5MB limit
      toast.error('File size must be less than 5MB');
      return;
    }
    
    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target.result;
      setBulkJson(content);
      handleBulkJsonChange(content);
    };
    reader.readAsText(file);
    setBulkFile(file);
  };

  const handleBulkProduce = () => {
    if (!selectedTopic) {
      toast.error('Please select a topic');
      return;
    }
    
    if (!bulkValidation.isValid) {
      toast.error('Please fix validation errors before proceeding');
      return;
    }
    
    try {
      const messages = JSON.parse(bulkJson);
      bulkProduceMutation.mutate({ topic: selectedTopic, messages, batchSize });
    } catch (error) {
      toast.error('Error parsing JSON');
    }
  };

  const handleClearBulk = () => {
    setBulkJson('');
    setBulkFile(null);
    setBulkValidation({ isValid: false, errors: [] });
    setBulkPreview([]);
  };

  const downloadTemplate = () => {
    const template = [
      {
        "key": "message-key-1",
        "value": "This is the first message content",
        "partition": 0,
        "headers": {
          "header1": "value1",
          "header2": "value2"
        }
      },
      {
        "key": "message-key-2",
        "value": "This is the second message content",
        "partition": 1
      },
      {
        "value": "Message without key and partition (will use defaults)"
      }
    ];
    
    const dataStr = JSON.stringify(template, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', 'kafka_messages_template.json');
    linkElement.click();
    
    toast.success('Template downloaded');
  };

  if (topicsLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Typography variant="h4" sx={{ mb: 2 }}>
        Message Producer
      </Typography>
      
      {/* Daily Message Limit Display */}
      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="body2">
          <strong>Daily Message Limit:</strong> You can produce up to 1,000 messages per day. 
          Remaining messages today: <strong>{remainingMessages}</strong>
        </Typography>
      </Alert>

      <Paper sx={{ width: '100%', mb: 2 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
        >
          <Tab label="Send Message" />
          <Tab label="Bulk Produce" />
          <Tab label="Message History" />
        </Tabs>
      </Paper>

      <TabPanel value={tabValue} index={0}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Compose Message
                </Typography>
                
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
                  <Autocomplete
                    fullWidth
                    options={accessibleTopics}
                    getOptionLabel={(option) => `${option.name} (${option.partitions} partitions)`}
                    value={accessibleTopics.find(t => t.name === selectedTopic) || null}
                    onChange={(event, newValue) => {
                      setSelectedTopic(newValue ? newValue.name : '');
                    }}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Select Topic"
                        placeholder="Search topics..."
                        required
                        variant="outlined"
                        size={isSmallScreen ? "small" : "medium"}
                      />
                    )}
                    renderOption={(props, option) => (
                      <Box component="li" {...props}>
                        <Box>
                          <Typography variant="body1" sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
                            {option.name}
                          </Typography>
                          <Typography variant="body2" color="text.secondary" sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>
                            {option.partitions} partitions
                          </Typography>
                        </Box>
                      </Box>
                    )}
                    filterOptions={(options, { inputValue }) =>
                      options.filter(option =>
                        option.name.toLowerCase().includes(inputValue.toLowerCase())
                      )
                    }
                    noOptionsText="No topics found"
                  />

                  <TextField
                    fullWidth
                    label="Message Key (optional)"
                    value={messageKey}
                    onChange={(e) => setMessageKey(e.target.value)}
                    placeholder="Enter message key"
                  />

                  <TextField
                    fullWidth
                    label="Message Value"
                    value={messageValue}
                    onChange={(e) => setMessageValue(e.target.value)}
                    placeholder="Enter message content"
                    multiline
                    rows={6}
                    required
                  />

                  <TextField
                    fullWidth
                    label="Headers (JSON format)"
                    value={headers}
                    onChange={(e) => setHeaders(e.target.value)}
                    placeholder='{"header1": "value1", "header2": "value2"}'
                    multiline
                    rows={3}
                  />

                  <Box sx={{ display: 'flex', gap: 2 }}>
                    <Button
                      variant="contained"
                      startIcon={<Send />}
                      onClick={handleSendMessage}
                      disabled={produceMutation.isLoading}
                    >
                      {produceMutation.isLoading ? 'Sending...' : 'Send Message'}
                    </Button>
                    <Button
                      variant="outlined"
                      startIcon={<Clear />}
                      onClick={handleClearForm}
                    >
                      Clear
                    </Button>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Message Preview
                </Typography>
                
                <Box sx={{ backgroundColor: '#f5f5f5', p: 2, borderRadius: 1 }}>
                  <Typography variant="body2" color="textSecondary">
                    Topic: {selectedTopic || 'Not selected'}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Key: {messageKey || 'null'}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Value: {messageValue || 'Empty'}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Headers: {headers || '{}'}
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Bulk Message Producer
                </Typography>
                
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
                  <Autocomplete
                    fullWidth
                    options={accessibleTopics}
                    getOptionLabel={(option) => `${option.name} (${option.partitions} partitions)`}
                    value={accessibleTopics.find(t => t.name === selectedTopic) || null}
                    onChange={(event, newValue) => {
                      setSelectedTopic(newValue ? newValue.name : '');
                    }}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Select Topic"
                        placeholder="Search topics..."
                        required
                        variant="outlined"
                        size={isSmallScreen ? "small" : "medium"}
                      />
                    )}
                    renderOption={(props, option) => (
                      <Box component="li" {...props}>
                        <Box>
                          <Typography variant="body1" sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
                            {option.name}
                          </Typography>
                          <Typography variant="body2" color="text.secondary" sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>
                            {option.partitions} partitions
                          </Typography>
                        </Box>
                      </Box>
                    )}
                    filterOptions={(options, { inputValue }) =>
                      options.filter(option =>
                        option.name.toLowerCase().includes(inputValue.toLowerCase())
                      )
                    }
                    noOptionsText="No topics found"
                  />

                  <FormControl fullWidth>
                    <InputLabel>Batch Size</InputLabel>
                    <Select
                      value={batchSize}
                      onChange={(e) => setBatchSize(e.target.value)}
                      label="Batch Size"
                      size={isSmallScreen ? "small" : "medium"}
                    >
                      <MenuItem value={5}>5 messages per batch</MenuItem>
                      <MenuItem value={10}>10 messages per batch</MenuItem>
                      <MenuItem value={25}>25 messages per batch</MenuItem>
                      <MenuItem value={50}>50 messages per batch</MenuItem>
                      <MenuItem value={100}>100 messages per batch</MenuItem>
                    </Select>
                  </FormControl>

                  <Divider />

                  <Stack direction="row" spacing={2} sx={{ mb: 2 }}>
                    <Button
                      variant={bulkMode === 'paste' ? 'contained' : 'outlined'}
                      startIcon={<ContentPaste />}
                      onClick={() => setBulkMode('paste')}
                      size={isSmallScreen ? "small" : "medium"}
                    >
                      Paste JSON
                    </Button>
                    <Button
                      variant={bulkMode === 'upload' ? 'contained' : 'outlined'}
                      startIcon={<Upload />}
                      component="label"
                      size={isSmallScreen ? "small" : "medium"}
                    >
                      Upload File
                      <input
                        type="file"
                        accept=".json"
                        hidden
                        onChange={handleFileUpload}
                      />
                    </Button>
                    <Button
                      variant="outlined"
                      startIcon={<Download />}
                      onClick={downloadTemplate}
                      size={isSmallScreen ? "small" : "medium"}
                    >
                      Download Template
                    </Button>
                  </Stack>

                  {bulkMode === 'paste' && (
                    <TextField
                      fullWidth
                      label="Messages JSON"
                      value={bulkJson}
                      onChange={(e) => handleBulkJsonChange(e.target.value)}
                      placeholder='[{"key": "msg1", "value": "Hello World"}, {"value": "Another message"}]'
                      multiline
                      rows={12}
                      error={!bulkValidation.isValid && bulkJson.length > 0}
                      helperText={
                        bulkJson.length > 0 
                          ? bulkValidation.isValid 
                            ? `${bulkPreview.length} messages ready to send`
                            : bulkValidation.errors.join(', ')
                          : 'Paste JSON array of messages or upload a file'
                      }
                    />
                  )}

                  {bulkMode === 'upload' && bulkFile && (
                    <Alert severity="info">
                      File loaded: {bulkFile.name} ({bulkFile.size} bytes)
                    </Alert>
                  )}

                  {bulkValidation.isValid && bulkPreview.length > 0 && (
                    <Accordion defaultExpanded>
                      <AccordionSummary expandIcon={<ExpandMore />}>
                        <Typography variant="subtitle1" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <CheckCircle color="success" />
                          Preview ({bulkPreview.length} messages)
                        </Typography>
                      </AccordionSummary>
                      <AccordionDetails>
                        <Box sx={{ maxHeight: 300, overflowY: 'auto' }}>
                          {bulkPreview.map((msg, index) => (
                            <Card key={index} sx={{ mb: 1, p: 1 }}>
                              <Typography variant="body2" color="textSecondary">
                                <strong>Key:</strong> {msg.key}
                              </Typography>
                              <Typography variant="body2" color="textSecondary">
                                <strong>Value:</strong> {msg.value}
                              </Typography>
                              <Typography variant="body2" color="textSecondary">
                                <strong>Partition:</strong> {msg.partition}
                              </Typography>
                              {Object.keys(msg.headers).length > 0 && (
                                <Typography variant="body2" color="textSecondary">
                                  <strong>Headers:</strong> {JSON.stringify(msg.headers)}
                                </Typography>
                              )}
                            </Card>
                          ))}
                        </Box>
                      </AccordionDetails>
                    </Accordion>
                  )}

                  {!bulkValidation.isValid && bulkValidation.errors.length > 0 && (
                    <Alert severity="error">
                      <Typography variant="body2">
                        {bulkValidation.errors.slice(0, 5).join(', ')}
                        {bulkValidation.errors.length > 5 && ` and ${bulkValidation.errors.length - 5} more errors`}
                      </Typography>
                    </Alert>
                  )}

                  <Stack direction="row" spacing={2}>
                    <Button
                      variant="contained"
                      startIcon={<Send />}
                      onClick={handleBulkProduce}
                      disabled={bulkProduceMutation.isLoading || !selectedTopic || !bulkValidation.isValid}
                      fullWidth
                      size={isSmallScreen ? "small" : "medium"}
                    >
                      {bulkProduceMutation.isLoading ? 'Producing...' : `Produce ${bulkPreview.length} Messages`}
                    </Button>
                    <Button
                      variant="outlined"
                      startIcon={<Clear />}
                      onClick={handleClearBulk}
                      disabled={bulkProduceMutation.isLoading}
                      size={isSmallScreen ? "small" : "medium"}
                    >
                      Clear
                    </Button>
                  </Stack>

                  {bulkProduceMutation.isLoading && (
                    <Box sx={{ width: '100%' }}>
                      <LinearProgress />
                      <Typography variant="body2" sx={{ mt: 1, textAlign: 'center' }}>
                        Producing messages in batches...
                      </Typography>
                    </Box>
                  )}
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Bulk Produce Info
                </Typography>
                
                <Box sx={{ backgroundColor: '#f5f5f5', p: 2, borderRadius: 1 }}>
                  <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
                    <strong>Topic:</strong> {selectedTopic || 'Not selected'}
                  </Typography>
                  <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
                    <strong>Batch Size:</strong> {batchSize} messages
                  </Typography>
                  <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
                    <strong>Status:</strong> {
                      bulkValidation.isValid 
                        ? <Chip label="Ready" color="success" size="small" />
                        : bulkJson.length > 0 
                          ? <Chip label="Invalid" color="error" size="small" />
                          : <Chip label="Waiting" color="default" size="small" />
                    }
                  </Typography>
                  {bulkValidation.isValid && (
                    <Typography variant="body2" color="textSecondary">
                      <strong>Messages:</strong> {bulkPreview.length} ready to send
                    </Typography>
                  )}
                </Box>

                <Alert severity="info" sx={{ mt: 2 }}>
                  <Typography variant="body2">
                    <strong>JSON Format:</strong><br />
                    Array of message objects with:<br />
                    • <code>value</code> (required): message content<br />
                    • <code>key</code> (optional): message key<br />
                    • <code>partition</code> (optional): specific partition<br />
                    • <code>headers</code> (optional): message headers
                  </Typography>
                </Alert>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      <TabPanel value={tabValue} index={2}>
        <Typography variant="h6" gutterBottom>
          Recent Messages
        </Typography>
        
        {messageHistory.length === 0 ? (
          <Alert severity="info">
            No messages sent yet. Send your first message to see it here.
          </Alert>
        ) : (
          <Grid container spacing={2}>
            {messageHistory.map((message) => (
              <Grid item xs={12} key={message.id}>
                <Card>
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                      <Typography variant="h6">
                        {message.topic}
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="body2" color="textSecondary">
                          {new Date(message.timestamp).toLocaleString()}
                        </Typography>
                        <Box
                          sx={{
                            width: 8,
                            height: 8,
                            borderRadius: '50%',
                            backgroundColor: message.status === 'success' ? 'success.main' : 'error.main',
                          }}
                        />
                      </Box>
                    </Box>
                    
                    <Typography variant="body2" color="textSecondary">
                      Key: {message.key || 'null'}
                    </Typography>
                    <Typography variant="body2" sx={{ mt: 1 }}>
                      {message.value}
                    </Typography>
                    
                    {message.status === 'error' && (
                      <Alert severity="error" sx={{ mt: 1 }}>
                        {message.error}
                      </Alert>
                    )}
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}
      </TabPanel>
    </Box>
  );
};

export default Producer; 