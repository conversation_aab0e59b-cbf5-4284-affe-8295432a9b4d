import React from 'react';
import { usePermissions } from '../contexts/PermissionsContext';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Chip,
  Alert,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
} from '@mui/material';
import {
  Storage,
  CheckCircle,
  Error,
} from '@mui/icons-material';
import { useClusterInfo } from '../hooks/useClusterInfo';

const ClusterInfo = () => {
  const { isSuperAdmin, loading: permissionsLoading } = usePermissions();

  const { data: clusterInfo, isLoading: infoLoading, error: infoError } = useClusterInfo({
    enabled: !permissionsLoading && isSuperAdmin() // Only fetch when user is confirmed to be super admin
  });

  // Only Super Admins can access cluster info
  if (permissionsLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (!isSuperAdmin()) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          Access denied. Only Super Administrators can access Cluster Information.
        </Alert>
      </Box>
    );
  }

  if (infoLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (infoError) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          <Typography variant="h6" gutterBottom>
            Error loading cluster information
          </Typography>
          <Typography variant="body2">
            {infoError?.message || 'Unknown error'}
          </Typography>
          <Typography variant="body2" sx={{ mt: 1 }}>
            This could be due to:
          </Typography>
          <ul style={{ margin: '8px 0 0 0', paddingLeft: '20px' }}>
            <li>Kafka cluster is not accessible</li>
            <li>Network connectivity issues</li>
            <li>Authentication problems</li>
            <li>Environment configuration issues</li>
          </ul>
        </Alert>
      </Box>
    );
  }

  const isHealthy = clusterInfo?.health?.status === 'healthy';

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Typography variant="h4" sx={{ mb: 4 }}>
        Cluster Information
      </Typography>

      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Storage sx={{ mr: 2, color: 'primary.main' }} />
                <Typography variant="h6">
                  Cluster Overview
                </Typography>
              </Box>

              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="body2" color="textSecondary">
                        Cluster ID:
                      </Typography>
                      <Typography variant="body2">
                        {clusterInfo?.clusterId || 'N/A'}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="body2" color="textSecondary">
                        Total Brokers:
                      </Typography>
                      <Typography variant="body2">
                        {clusterInfo?.brokers?.length || 0}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="body2" color="textSecondary">
                        Total Topics:
                      </Typography>
                      <Typography variant="body2">
                        {clusterInfo?.topics || 0}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="body2" color="textSecondary">
                        Status:
                      </Typography>
                      <Chip
                        icon={isHealthy ? <CheckCircle /> : <Error />}
                        label={isHealthy ? 'Healthy' : 'Unhealthy'}
                        color={isHealthy ? 'success' : 'error'}
                        size="small"
                      />
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="body2" color="textSecondary">
                        Connection:
                      </Typography>
                      <Chip
                        label={clusterInfo?.health?.connected ? 'Connected' : 'Disconnected'}
                        color={clusterInfo?.health?.connected ? 'success' : 'error'}
                        size="small"
                      />
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="body2" color="textSecondary">
                        Last Updated:
                      </Typography>
                      <Typography variant="body2">
                        {new Date(clusterInfo?.health?.timestamp || Date.now()).toLocaleString()}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Broker Details
              </Typography>

              {clusterInfo?.brokers?.length === 0 ? (
                <Alert severity="warning">
                  No brokers found in the cluster.
                </Alert>
              ) : (
                <TableContainer component={Paper}>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Node ID</TableCell>
                        <TableCell>Host</TableCell>
                        <TableCell>Port</TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell>Rack</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {clusterInfo?.brokers?.map((broker) => (
                        <TableRow key={broker.nodeId}>
                          <TableCell>
                            <Chip
                              label={broker.nodeId}
                              size="small"
                              color="primary"
                            />
                          </TableCell>
                          <TableCell>{broker.host}</TableCell>
                          <TableCell>{broker.port}</TableCell>
                          <TableCell>
                            <Chip
                              icon={<CheckCircle />}
                              label="Online"
                              color="success"
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            {broker.rack || 'N/A'}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              )}
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Connection Details
              </Typography>

              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Typography variant="body2" color="textSecondary" gutterBottom>
                    Broker Endpoints:
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                    {clusterInfo?.brokers?.map((broker, index) => (
                      <Chip
                        key={index}
                        label={`${broker.host}:${broker.port}`}
                        size="small"
                        variant="outlined"
                        color="primary"
                      />
                    ))}
                  </Box>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="body2" color="textSecondary" gutterBottom>
                    Connection Status:
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                    <Chip
                      icon={clusterInfo?.health?.connected ? <CheckCircle /> : <Error />}
                      label={clusterInfo?.health?.connected ? 'Connected' : 'Disconnected'}
                      color={clusterInfo?.health?.connected ? 'success' : 'error'}
                      size="small"
                    />
                    <Typography variant="body2" color="textSecondary">
                      Health check interval: 10 seconds
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default ClusterInfo; 