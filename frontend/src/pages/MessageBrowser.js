import React, { useState } from 'react';
import { usePermissions, PERMISSIONS } from '../contexts/PermissionsContext';
import {
  Box,
  Typography,
  Card,
  CardContent,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Alert,
  CircularProgress,
  LinearProgress,
  Autocomplete,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  FormControlLabel,
  Switch,
  Divider,
  useTheme,
  useMediaQuery,
  Stack,
} from '@mui/material';
import {
  Search,
  Refresh,
  Download,
  Clear,
  FilterList,
  ExpandMore,
} from '@mui/icons-material';
import { useMutation } from 'react-query';
import toast from 'react-hot-toast';
import { topicsApi } from '../services/api';
import { useTopics } from '../hooks/useTopics';

const MessageBrowser = () => {
  const [selectedTopic, setSelectedTopic] = useState('');
  const [partition, setPartition] = useState('all');
  const [offset, setOffset] = useState(0);
  const [limit, setLimit] = useState(100);
  const [messages, setMessages] = useState([]);

  const { hasPermission, getAccessibleTopics, loading: permissionsLoading } = usePermissions();
  const [searchParams, setSearchParams] = useState(null);
  const [hasSearched, setHasSearched] = useState(false);
  
  // Search filters
  const [searchKey, setSearchKey] = useState('');
  const [searchValue, setSearchValue] = useState('');
  const [startTimestamp, setStartTimestamp] = useState('');
  const [endTimestamp, setEndTimestamp] = useState('');
  const [caseSensitive, setCaseSensitive] = useState(false);
  const [useSearchMode, setUseSearchMode] = useState(false);

  const theme = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('sm'));

  const { data: topics, isLoading: topicsLoading } = useTopics();

  // Filter topics based on user permissions
  const accessibleTopics = getAccessibleTopics(topics || []);

  const searchMutation = useMutation(
    ({ topic, params, isSearch }) => {
      if (isSearch) {
        return topicsApi.searchMessages(topic, params);
      } else {
        return topicsApi.getMessages(topic, params);
      }
    },
    {
      onSuccess: (response) => {
        const messages = response.data || [];
        setMessages(messages);
        const action = useSearchMode ? 'Found' : 'Fetched';
        toast.success(`${action} ${messages.length} messages`);
      },
      onError: (error) => {
        const action = useSearchMode ? 'searching' : 'fetching';
        toast.error(`Error ${action} messages: ${error.message}`);
        setMessages([]);
      },
    }
  );

  // Check if user has permission to view messages
  if (permissionsLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (!hasPermission(PERMISSIONS.VIEW_MESSAGES)) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          Access denied. You do not have permission to view messages.
        </Alert>
      </Box>
    );
  }

  const handleSearch = () => {
    if (!selectedTopic) {
      toast.error('Please select a topic');
      return;
    }

    const params = {
      partition: partition === 'all' ? -1 : parseInt(partition),
      offset: parseInt(offset),
      limit: parseInt(limit),
    };

    // Add search filters if in search mode
    if (useSearchMode) {
      if (searchKey.trim()) params.key = searchKey.trim();
      if (searchValue.trim()) params.value = searchValue.trim();
      if (startTimestamp) params.startTimestamp = startTimestamp;
      if (endTimestamp) params.endTimestamp = endTimestamp;
      params.caseSensitive = caseSensitive.toString();
    }

    setSearchParams({ topic: selectedTopic, ...params, isSearch: useSearchMode });
    setHasSearched(true);
    searchMutation.mutate({ topic: selectedTopic, params, isSearch: useSearchMode });
  };

  const handleClear = () => {
    setSelectedTopic('');
    setPartition('all');
    setOffset(0);
    setLimit(100);
    setMessages([]);
    setSearchParams(null);
    setSearchKey('');
    setSearchValue('');
    setStartTimestamp('');
    setEndTimestamp('');
    setCaseSensitive(false);
    setUseSearchMode(false);
    setHasSearched(false);
  };

  const handleExport = () => {
    if (messages.length === 0) {
      toast.error('No messages to export');
      return;
    }

    const dataStr = JSON.stringify(messages, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
    
    const exportFileDefaultName = `${selectedTopic}_messages_${new Date().toISOString().split('T')[0]}.json`;
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
    
    toast.success('Messages exported successfully');
  };

  const formatValue = (value) => {
    if (!value) return 'null';
    if (value.length > 100) {
      return value.substring(0, 100) + '...';
    }
    return value;
  };

  const selectedTopicData = topics?.find(t => t.name === selectedTopic);

  if (topicsLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Typography 
        variant={isSmallScreen ? "h5" : "h4"} 
        sx={{ 
          mb: { xs: 2, sm: 4 },
          fontSize: { xs: '1.5rem', sm: '2.125rem' },
          fontWeight: 600,
        }}
      >
        Message Browser
      </Typography>

      <Grid container spacing={{ xs: 2, sm: 3 }}>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
              <Typography 
                variant="h6" 
                gutterBottom
                sx={{ 
                  fontSize: { xs: '1.125rem', sm: '1.25rem' },
                  fontWeight: 600,
                }}
              >
                Search Parameters
              </Typography>
              
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Autocomplete
                  fullWidth
                  options={accessibleTopics}
                  getOptionLabel={(option) => `${option.name} (${option.partitions} partitions)`}
                  value={accessibleTopics.find(t => t.name === selectedTopic) || null}
                  onChange={(event, newValue) => {
                    setSelectedTopic(newValue ? newValue.name : '');
                  }}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Select Topic"
                      placeholder="Search topics..."
                      required
                      variant="outlined"
                      size={isSmallScreen ? "small" : "medium"}
                    />
                  )}
                  renderOption={(props, option) => (
                    <Box component="li" {...props}>
                      <Box>
                        <Typography variant="body1" sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
                          {option.name}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>
                          {option.partitions} partitions
                        </Typography>
                      </Box>
                    </Box>
                  )}
                  filterOptions={(options, { inputValue }) =>
                    options.filter(option =>
                      option.name.toLowerCase().includes(inputValue.toLowerCase())
                    )
                  }
                  noOptionsText="No topics found"
                />

                <FormControl fullWidth>
                  <InputLabel>Partition</InputLabel>
                  <Select
                    value={partition}
                    onChange={(e) => setPartition(e.target.value)}
                    label="Partition"
                    size={isSmallScreen ? "small" : "medium"}
                  >
                    <MenuItem value="all">All Partitions</MenuItem>
                    {selectedTopicData && [...Array(selectedTopicData.partitions)].map((_, index) => (
                      <MenuItem key={index} value={index}>
                        Partition {index}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>

                <TextField
                  fullWidth
                  label="Offset"
                  type="number"
                  value={offset}
                  onChange={(e) => setOffset(parseInt(e.target.value) || 0)}
                  inputProps={{ min: 0 }}
                  helperText="Starting offset (0 = beginning)"
                  size={isSmallScreen ? "small" : "medium"}
                />

                <TextField
                  fullWidth
                  label="Limit"
                  type="number"
                  value={limit}
                  onChange={(e) => setLimit(parseInt(e.target.value) || 100)}
                  inputProps={{ min: 1, max: 1000 }}
                  helperText="Maximum number of messages"
                  size={isSmallScreen ? "small" : "medium"}
                />

                <Divider />

                <FormControlLabel
                  control={
                    <Switch
                      checked={useSearchMode}
                      onChange={(e) => setUseSearchMode(e.target.checked)}
                      size={isSmallScreen ? "small" : "medium"}
                    />
                  }
                  label="Enable Advanced Search"
                  sx={{ 
                    '& .MuiFormControlLabel-label': {
                      fontSize: { xs: '0.875rem', sm: '1rem' }
                    }
                  }}
                />

                {useSearchMode && (
                  <Accordion defaultExpanded>
                    <AccordionSummary expandIcon={<ExpandMore />}>
                      <Typography variant="subtitle1" sx={{ 
                        display: 'flex', 
                        alignItems: 'center', 
                        gap: 1,
                        fontSize: { xs: '0.875rem', sm: '1rem' }
                      }}>
                        <FilterList />
                        Search Filters
                      </Typography>
                    </AccordionSummary>
                    <AccordionDetails>
                      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                        <TextField
                          fullWidth
                          label="Search in Key"
                          value={searchKey}
                          onChange={(e) => setSearchKey(e.target.value)}
                          placeholder="Enter key to search for..."
                          helperText="Leave empty to search all keys"
                          size={isSmallScreen ? "small" : "medium"}
                        />

                        <TextField
                          fullWidth
                          label="Search in Value"
                          value={searchValue}
                          onChange={(e) => setSearchValue(e.target.value)}
                          placeholder="Enter value to search for..."
                          helperText="Leave empty to search all values"
                          size={isSmallScreen ? "small" : "medium"}
                        />

                        <TextField
                          fullWidth
                          label="Start Timestamp"
                          type="datetime-local"
                          value={startTimestamp}
                          onChange={(e) => setStartTimestamp(e.target.value)}
                          InputLabelProps={{ shrink: true }}
                          helperText="Messages from this time onwards"
                          size={isSmallScreen ? "small" : "medium"}
                        />

                        <TextField
                          fullWidth
                          label="End Timestamp"
                          type="datetime-local"
                          value={endTimestamp}
                          onChange={(e) => setEndTimestamp(e.target.value)}
                          InputLabelProps={{ shrink: true }}
                          helperText="Messages up to this time"
                          size={isSmallScreen ? "small" : "medium"}
                        />

                        <FormControlLabel
                          control={
                            <Switch
                              checked={caseSensitive}
                              onChange={(e) => setCaseSensitive(e.target.checked)}
                              size={isSmallScreen ? "small" : "medium"}
                            />
                          }
                          label="Case Sensitive Search"
                          sx={{ 
                            '& .MuiFormControlLabel-label': {
                              fontSize: { xs: '0.875rem', sm: '1rem' }
                            }
                          }}
                        />
                      </Box>
                    </AccordionDetails>
                  </Accordion>
                )}

                <Stack direction={{ xs: 'column', sm: 'row' }} spacing={1}>
                  <Button
                    variant="contained"
                    startIcon={<Search />}
                    onClick={handleSearch}
                    disabled={searchMutation.isLoading || !selectedTopic}
                    fullWidth
                    size={isSmallScreen ? "small" : "medium"}
                  >
                    {searchMutation.isLoading ? 'Searching...' : (useSearchMode ? 'Search' : 'Fetch')}
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<Clear />}
                    onClick={handleClear}
                    disabled={searchMutation.isLoading}
                    size={isSmallScreen ? "small" : "medium"}
                  >
                    Clear
                  </Button>
                </Stack>

                {searchParams && (
                  <Alert severity="info" sx={{ mt: 1 }}>
                    <Typography variant="body2" sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>
                      <strong>Last {useSearchMode ? 'Search' : 'Fetch'}:</strong><br />
                      Topic: {searchParams.topic}<br />
                      Partition: {searchParams.partition === -1 ? 'All' : searchParams.partition}<br />
                      Offset: {searchParams.offset}<br />
                      Limit: {searchParams.limit}
                      {useSearchMode && searchParams.key && <><br />Key: "{searchParams.key}"</>}
                      {useSearchMode && searchParams.value && <><br />Value: "{searchParams.value}"</>}
                      {useSearchMode && searchParams.startTimestamp && <><br />From: {searchParams.startTimestamp}</>}
                      {useSearchMode && searchParams.endTimestamp && <><br />To: {searchParams.endTimestamp}</>}
                    </Typography>
                  </Alert>
                )}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={8}>
          <Card>
            <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
              <Box sx={{ 
                display: 'flex', 
                flexDirection: { xs: 'column', sm: 'row' },
                justifyContent: 'space-between', 
                alignItems: { xs: 'stretch', sm: 'center' }, 
                mb: 2,
                gap: { xs: 1, sm: 0 }
              }}>
                <Typography 
                  variant="h6"
                  sx={{ 
                    fontSize: { xs: '1.125rem', sm: '1.25rem' },
                    fontWeight: 600,
                  }}
                >
                  Messages ({messages.length})
                  {useSearchMode && searchParams && (
                    <Chip 
                      label="Search Results" 
                      size="small" 
                      color="primary" 
                      sx={{ ml: 1 }}
                    />
                  )}
                </Typography>
                <Stack direction="row" spacing={1}>
                  <Button
                    variant="outlined"
                    startIcon={<Download />}
                    onClick={handleExport}
                    disabled={messages.length === 0}
                    size={isSmallScreen ? "small" : "medium"}
                  >
                    Export
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<Refresh />}
                    onClick={handleSearch}
                    disabled={!selectedTopic || searchMutation.isLoading}
                    size={isSmallScreen ? "small" : "medium"}
                  >
                    Refresh
                  </Button>
                </Stack>
              </Box>

              {searchMutation.isLoading && (
                <Box sx={{ width: '100%', mb: 2 }}>
                  <LinearProgress />
                </Box>
              )}

              {!selectedTopic ? (
                <Alert severity="info">
                  Please select a topic and configure search parameters to browse messages.
                </Alert>
              ) : hasSearched && messages.length === 0 && !searchMutation.isLoading ? (
                <Alert severity="warning">
                  {useSearchMode 
                    ? 'No messages found matching your search criteria. Try adjusting your filters or check if the topic contains any matching messages.'
                    : 'No messages found. Try adjusting your search parameters or check if the topic contains any messages.'
                  }
                </Alert>
              ) : (
                <Box sx={{ overflowX: 'auto' }}>
                  <TableContainer component={Paper} sx={{ maxHeight: 600 }}>
                    <Table stickyHeader size={isSmallScreen ? "small" : "medium"}>
                      <TableHead>
                        <TableRow>
                          <TableCell sx={{ 
                            fontSize: { xs: '0.75rem', sm: '0.875rem' },
                            fontWeight: 600,
                            minWidth: { xs: 60, sm: 80 }
                          }}>
                            Partition
                          </TableCell>
                          <TableCell sx={{ 
                            fontSize: { xs: '0.75rem', sm: '0.875rem' },
                            fontWeight: 600,
                            minWidth: { xs: 60, sm: 80 }
                          }}>
                            Offset
                          </TableCell>
                          <TableCell sx={{ 
                            fontSize: { xs: '0.75rem', sm: '0.875rem' },
                            fontWeight: 600,
                            minWidth: { xs: 100, sm: 120 }
                          }}>
                            Key
                          </TableCell>
                          <TableCell sx={{ 
                            fontSize: { xs: '0.75rem', sm: '0.875rem' },
                            fontWeight: 600,
                            minWidth: { xs: 150, sm: 200 }
                          }}>
                            Value
                          </TableCell>
                          <TableCell sx={{ 
                            fontSize: { xs: '0.75rem', sm: '0.875rem' },
                            fontWeight: 600,
                            minWidth: { xs: 120, sm: 140 }
                          }}>
                            Timestamp
                          </TableCell>
                          <TableCell sx={{ 
                            fontSize: { xs: '0.75rem', sm: '0.875rem' },
                            fontWeight: 600,
                            minWidth: { xs: 80, sm: 100 }
                          }}>
                            Headers
                          </TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {messages.map((message, index) => (
                          <TableRow key={`${message.partition}-${message.offset}-${index}`}>
                            <TableCell>
                              <Chip 
                                label={message.partition} 
                                size="small" 
                                color="primary"
                                sx={{ 
                                  fontSize: { xs: '0.75rem', sm: '0.875rem' },
                                  height: { xs: 20, sm: 24 }
                                }}
                              />
                            </TableCell>
                            <TableCell>
                              <Typography 
                                variant="body2" 
                                fontFamily="monospace"
                                sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}
                              >
                                {message.offset}
                              </Typography>
                            </TableCell>
                            <TableCell>
                              <Typography 
                                variant="body2" 
                                fontFamily="monospace"
                                sx={{ 
                                  fontSize: { xs: '0.75rem', sm: '0.875rem' },
                                  wordBreak: 'break-word'
                                }}
                              >
                                {message.key || 'null'}
                              </Typography>
                            </TableCell>
                            <TableCell>
                              <Box sx={{ maxWidth: { xs: 150, sm: 300 }, wordBreak: 'break-word' }}>
                                <Typography 
                                  variant="body2" 
                                  fontFamily="monospace"
                                  sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}
                                >
                                  {formatValue(message.value)}
                                </Typography>
                              </Box>
                            </TableCell>
                            <TableCell>
                              <Typography 
                                variant="body2"
                                sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}
                              >
                                {new Date(parseInt(message.timestamp)).toLocaleString()}
                              </Typography>
                            </TableCell>
                            <TableCell>
                              {message.headers && Object.keys(message.headers).length > 0 ? (
                                <Box>
                                  {Object.entries(message.headers).map(([key, value]) => (
                                    <Chip
                                      key={key}
                                      label={`${key}: ${value}`}
                                      size="small"
                                      variant="outlined"
                                      sx={{ mr: 0.5, mb: 0.5 }}
                                    />
                                  ))}
                                </Box>
                              ) : (
                                <Typography variant="body2" color="textSecondary">
                                  No headers
                                </Typography>
                              )}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default MessageBrowser; 