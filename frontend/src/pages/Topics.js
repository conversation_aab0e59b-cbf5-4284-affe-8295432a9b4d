import React, { useState, useEffect } from 'react';
import { usePermissions } from '../contexts/PermissionsContext';
import {
  Box,
  Typography,
  <PERSON>ton,
  Card,
  Chip,
  IconButton,
  Alert,
  CircularProgress,
  InputAdornment,
  TextField,
  useTheme,
  useMediaQuery,
  Stack,
} from '@mui/material';
import {
  Visibility,
  Topic as TopicIcon,
  Search,
  Clear,
  Message as MessageIcon,
  Refresh,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import toast from 'react-hot-toast';
import { topicsApi } from '../services/api';
import { useTopics } from '../hooks/useTopics';
import { FixedSizeList as List } from 'react-window';

const useFilteredTopics = (topics = [], searchTerm = '') => {
  const [filtered, setFiltered] = useState([]);

  useEffect(() => {
    if (!searchTerm || !topics.length) {
      setFiltered(topics);
      return;
    }

    const timeout = setTimeout(() => {
      const lower = searchTerm.toLowerCase();
      const result = topics.filter((t) =>
        t.name.toLowerCase().includes(lower)
      );
      setFiltered(result);
    }, 100);

    return () => clearTimeout(timeout);
  }, [topics, searchTerm]);

  return filtered;
};

// CreateTopicDialog component removed for VAPT compliance

const TopicCard = React.memo(({ topic, onDelete, onView, onConfigure }) => {
  const [isLoadingCount, setIsLoadingCount] = useState(false);
  const [messageCount, setMessageCount] = useState(topic.totalMessages);
  const [partitionDetails, setPartitionDetails] = useState(topic.partitionDetails);

  const theme = useTheme();
  // Removed redundant usePermissions call that was causing timing issues
  // const { isSuperAdmin, isTopicManager } = usePermissions();

  const formatNumber = (num) => {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + "M";
    if (num >= 1000) return (num / 1000).toFixed(1) + "K";
    return num?.toString() || "0";
  };

  const handleLoadMessageCount = async () => {
    if (messageCount !== undefined || isLoadingCount) return;

    setIsLoadingCount(true);
    try {
      const response = await topicsApi.getMessageCount(topic.name);
      setMessageCount(response.data.totalMessages);
      setPartitionDetails(response.data.partitionDetails);
      toast.success(`Message count loaded for ${topic.name}`);
    } catch (error) {
      toast.error(`Failed to load message count: ${error.message}`);
    } finally {
      setIsLoadingCount(false);
    }
  };

  return (
    <Card
      sx={{
        px: 2,
        py: 2,
        display: "flex",
        flexDirection: "column",
        gap: 1.5,
        justifyContent: "space-between",
        '&:hover': {
          boxShadow: theme.shadows[4],
          transform: "translateY(-1px)",
          transition: "all 0.2s ease-in-out",
        },
      }}
    >
      {/* Top row: icon + name + actions */}
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          flexWrap: "wrap",
          gap: 1,
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center", gap: 1, minWidth: 0 }}>
          <TopicIcon sx={{ color: "primary.main", fontSize: 28 }} />
          <Typography
            variant="subtitle1"
            sx={{
              fontWeight: 600,
              fontSize: { xs: "1rem", sm: "1.125rem" },
              overflow: "hidden",
              whiteSpace: "nowrap",
              textOverflow: "ellipsis",
              maxWidth: "240px",
            }}
          >
            {topic.name}
          </Typography>
        </Box>

        {/* Action buttons - based on user permissions */}
        <Stack direction="row" spacing={0.5}>
          {/* View button - available to all users with topic access */}
          <IconButton size="small" onClick={() => onView(topic.name)}>
            <Visibility fontSize="small" />
          </IconButton>
          {/* Delete and Configure buttons removed for VAPT compliance */}
        </Stack>
      </Box>

      {/* Middle row: info chips */}
      <Stack
        direction="row"
        spacing={1}
        flexWrap="wrap"
        useFlexGap
        alignItems="center"
      >
        <Chip label={`${topic.partitions} partitions`} size="small" variant="outlined" />
        <Chip
          label={`${partitionDetails?.length || 0} replicas`}
          size="small"
          variant="outlined"
          color="secondary"
        />
        {messageCount !== undefined ? (
          <Chip
            label={`Messages: ${formatNumber(messageCount)}`}
            size="small"
            variant="outlined"
            color="success"
            icon={<MessageIcon fontSize="small" />}
          />
        ) : (
          <Button
            size="small"
            variant="outlined"
            startIcon={isLoadingCount ? <CircularProgress size={14} /> : <Refresh />}
            onClick={handleLoadMessageCount}
            sx={{
              fontSize: "0.75rem",
              px: 1.5,
              minWidth: "auto",
              whiteSpace: "nowrap",
            }}
          >
            {isLoadingCount ? "Loading..." : "Load Count"}
          </Button>
        )}
      </Stack>
    </Card>
  );
});

const Topics = () => {
  const [searchTerm, setSearchTerm] = useState('');

  const { getAccessibleTopics, loading: permissionsLoading, permissions } = usePermissions();

  const navigate = useNavigate();
  const theme = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('sm'));

  // Use the shared topics hook for consistent caching
  const { data: topics, isLoading: topicsLoading } = useTopics();

  // Calculate accessible topics only when both permissions and topics are available
  const accessibleTopics = React.useMemo(() => {
    if (!permissions || !topics) return [];
    return getAccessibleTopics(topics);
  }, [permissions, topics, getAccessibleTopics]);

  const filteredTopics = useFilteredTopics(accessibleTopics, searchTerm);

  // Topic creation and deletion mutations removed for VAPT compliance

  const handleViewTopic = React.useCallback((topicName) => {
    try {
      console.log('Navigating to topic:', topicName); // Debug log to confirm clicks are working
      navigate(`/topics/${topicName}`);
    } catch (error) {
      console.error('Navigation error:', error);
      toast.error(`Failed to navigate to topic: ${error.message}`);
    }
  }, [navigate]);

  // handleConfigureTopic removed for VAPT compliance

  // Show loading if either topics or permissions are loading
  if (topicsLoading || permissionsLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  // Don't render until we have both permissions and topics data
  if (!permissions || topics === undefined) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Box sx={{ 
        display: 'flex', 
        flexDirection: { xs: 'column', sm: 'row' },
        justifyContent: 'space-between', 
        alignItems: { xs: 'stretch', sm: 'center' }, 
        mb: { xs: 2, sm: 4 },
        gap: { xs: 2, sm: 0 }
      }}>
        <Typography 
          variant={isSmallScreen ? "h5" : "h4"}
          sx={{ 
            fontSize: { xs: '1.5rem', sm: '2.125rem' },
            fontWeight: 600,
          }}
        >
          Topics
        </Typography>
        {/* Create Topic button removed for VAPT compliance */}
      </Box>

      {/* Performance Notice */}
      {/* <Alert severity="info" sx={{ mb: { xs: 2, sm: 3 } }}>
        <Typography variant="body2" sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
          <strong>Performance Note:</strong> Message counts are loaded on-demand to improve page load speed. 
          Click "Load Count" on any topic card to see its message statistics.
        </Typography>
      </Alert> */}

      {/* Search Bar */}
      <Box sx={{ mb: { xs: 2, sm: 3 } }}>
        <TextField
          fullWidth
          placeholder="Search topics..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Search />
              </InputAdornment>
            ),
            endAdornment: searchTerm && (
              <InputAdornment position="end">
                <IconButton
                  aria-label="clear search"
                  onClick={() => setSearchTerm('')}
                  edge="end"
                  size="small"
                >
                  <Clear />
                </IconButton>
              </InputAdornment>
            ),
          }}
          variant="outlined"
          size={isSmallScreen ? "small" : "medium"}
        />
      </Box>

      {/* Results Info */}
      {searchTerm && (
        <Box sx={{ mb: 2 }}>
          <Typography variant="body2" color="text.secondary">
            {filteredTopics.length} topic(s) found for "{searchTerm}"
          </Typography>
        </Box>
      )}

      {topics?.length === 0 ? (
        <Alert severity="info" sx={{ mb: 2 }}>
          No topics found. Create your first topic to get started.
        </Alert>
      ) : accessibleTopics.length === 0 ? (
        <Alert severity="warning" sx={{ mb: 2 }}>
          You don't have access to any topics. Please contact your administrator to get topic access.
          <br />
          <small>Your role: {permissions.role}</small>
        </Alert>
      ) : filteredTopics.length === 0 && searchTerm ? (
        <Alert severity="info" sx={{ mb: 2 }}>
          No topics found matching "{searchTerm}". Try a different search term.
        </Alert>
      ) : (
        <List
          height={Math.min(900, window.innerHeight - 300)}
          itemCount={filteredTopics.length}
          itemSize={isSmallScreen ? 140 : 130}
          width="100%"
        >
          {({ index, style }) => {
            const topic = filteredTopics[index];
            return (
              <div style={style} key={topic.name}>
                <TopicCard
                  topic={topic}
                  onView={handleViewTopic}
                  onDelete={() => {}} // Removed for VAPT compliance
                  onConfigure={() => {}} // Removed for VAPT compliance
                />
              </div>
            );
          }}
        </List>
      )}

      {/* CreateTopicDialog and Delete confirmation dialog removed for VAPT compliance */}
    </Box>
  );
};

export default Topics; 