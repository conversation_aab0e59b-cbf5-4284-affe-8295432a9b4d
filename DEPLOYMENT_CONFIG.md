# Kafka Dashboard - Deployment Configuration Guide

## 🔧 **BROWSER-SAFE ENVIRONMENT DETECTION SYSTEM**

### **✅ Solution Implemented:**
We've implemented a browser-safe environment detection system that uses `window.location.hostname` instead of `process.env.NODE_ENV` to automatically use the correct API URL.

### **🎯 How It Works:**

```javascript
// Browser-safe environment detection
const hostname = window.location.hostname;
const isLocalhost = hostname === 'localhost' || hostname === '127.0.0.1';
const isProduction = hostname === 'kafkadashboard.policybazaar.com' || hostname.includes('policybazaar.com');

// API URL determination logic
if (MANUAL_API_URL) {
  // Manual override takes precedence
  API_BASE_URL = MANUAL_API_URL;
} else if (isProduction) {
  // Production: Always use the production domain
  API_BASE_URL = 'https://kafkadashboard.policybazaar.com/api';
} else if (isLocalhost) {
  // Development on localhost: Use localhost backend
  API_BASE_URL = 'http://localhost:5000/api';
} else {
  // Development on remote domain: Use proxy (relative URLs)
  API_BASE_URL = '/api';
}
```

### **🔧 Manual Override for Testing:**

```javascript
// In frontend/src/services/api.js
const MANUAL_API_URL = null; // Automatic detection
// OR
const MANUAL_API_URL = 'https://kafkadashboard.policybazaar.com/api'; // Test production locally
// OR  
const MANUAL_API_URL = 'http://localhost:5000/api'; // Force localhost
```

## 📋 **ENVIRONMENT CONFIGURATIONS**

### **1. Production Environment**

**Automatic Configuration:**
- **NODE_ENV**: `production`
- **API URL**: `https://kafkadashboard.policybazaar.com/api`
- **No Environment Variables Required**

**Build Process:**
```bash
# React automatically sets NODE_ENV=production
npm run build
```

### **2. Development Environment**

**Local Development (localhost):**
- **NODE_ENV**: `development`
- **Hostname**: `localhost` or `127.0.0.1`
- **API URL**: `http://localhost:5000/api`
- **Backend**: Must be running on localhost:5000

**Remote Development:**
- **NODE_ENV**: `development`
- **Hostname**: `kafkadashboard.policybazaar.com`
- **API URL**: `/api` (uses proxy)
- **Proxy**: `https://kafkadashboard.policybazaar.com`

### **3. Package.json Proxy Configuration**

```json
{
  "proxy": "https://kafkadashboard.policybazaar.com"
}
```

**When Proxy is Used:**
- Only in development mode
- Only when not on localhost
- React dev server forwards `/api/*` requests to the proxy

## 🔄 **HOW THE SYSTEM WORKS**

### **Production Mode (`npm run build`):**

1. **Environment Detection:**
   ```javascript
   NODE_ENV = 'production'
   isProduction = true
   ```

2. **API URL Selection:**
   ```javascript
   API_BASE_URL = 'https://kafkadashboard.policybazaar.com/api'
   ```

3. **Request Flow:**
   ```javascript
   axios.get('/topics')
   // → https://kafkadashboard.policybazaar.com/api/topics
   ```

### **Development Mode (`npm start` on localhost):**

1. **Environment Detection:**
   ```javascript
   NODE_ENV = 'development'
   hostname = 'localhost'
   isLocalhost = true
   ```

2. **API URL Selection:**
   ```javascript
   API_BASE_URL = 'http://localhost:5000/api'
   ```

3. **Request Flow:**
   ```javascript
   axios.get('/topics')
   // → http://localhost:5000/api/topics
   ```

### **Development Mode (`npm start` on remote domain):**

1. **Environment Detection:**
   ```javascript
   NODE_ENV = 'development'
   hostname = 'kafkadashboard.policybazaar.com'
   isLocalhost = false
   ```

2. **API URL Selection:**
   ```javascript
   API_BASE_URL = '/api'
   ```

3. **Request Flow:**
   ```javascript
   axios.get('/topics')
   // → React dev server proxies to https://kafkadashboard.policybazaar.com/api/topics
   ```

## 🚀 **DEPLOYMENT STEPS**

### **1. Local Development:**
```bash
# Start backend on localhost:5000
cd backend && npm start

# Start frontend (will auto-detect localhost)
cd frontend && npm start
```

### **2. Production Build:**
```bash
# Build for production (NODE_ENV automatically set to 'production')
npm run build

# Deploy the build folder
# No environment variables needed!
```

### **3. Docker Deployment:**
```dockerfile
# DockerfileProd
FROM node:16-alpine as build
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=build /app/build /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### **4. AWS ECS Deployment:**
```json
{
  "containerDefinitions": [
    {
      "name": "frontend",
      "image": "kafka-dashboard-frontend:latest",
      "environment": [
        {"name": "NODE_ENV", "value": "production"}
      ]
    }
  ]
}
```

## 🔍 **VERIFICATION STEPS**

### **1. Check Environment Detection:**
```javascript
// Open browser console and check:
console.log('API Configuration:', {
  nodeEnv: process.env.NODE_ENV,
  isDevelopment,
  isProduction,
  isLocalhost,
  hostname: window.location.hostname,
  apiBaseUrl: API_BASE_URL
});
```

### **2. Test API Connection:**
```bash
# Production
curl -f https://kafkadashboard.policybazaar.com/health

# Local Development
curl -f http://localhost:5000/health
```

### **3. Verify Build Output:**
```bash
# Check if production build uses correct URL
grep -r "kafkadashboard.policybazaar.com" frontend/build/
```

## 🛠️ **TROUBLESHOOTING**

### **Issue: Production still using localhost**

**Check:**
1. **Build process**: Ensure `npm run build` is used
2. **NODE_ENV**: Should be `production` in built files
3. **Console logs**: Check the API configuration log

### **Issue: Development not working on localhost**

**Check:**
1. **Backend running**: Ensure backend is on localhost:5000
2. **Console logs**: Check API configuration
3. **Network tab**: Verify requests go to localhost:5000

### **Issue: Development not working on remote domain**

**Check:**
1. **Proxy setting**: Verify package.json proxy URL
2. **React dev server**: Should be running
3. **Network tab**: Verify requests are proxied

## 📊 **MONITORING**

### **Debug Information:**
The system logs API configuration on startup:
```javascript
API Configuration: {
  nodeEnv: "production",
  isDevelopment: false,
  isProduction: true,
  isLocalhost: false,
  hostname: "kafkadashboard.policybazaar.com",
  apiBaseUrl: "https://kafkadashboard.policybazaar.com/api"
}
```

### **Health Check Endpoints:**
- **Production:** `https://kafkadashboard.policybazaar.com/health`
- **Local Development:** `http://localhost:5000/health`

## ✅ **CONFIGURATION CHECKLIST**

- [x] Environment detection implemented
- [x] Production uses hardcoded domain
- [x] Localhost development uses localhost:5000
- [x] Remote development uses proxy
- [x] No environment variables required
- [x] Console logging for debugging
- [x] Automatic NODE_ENV detection
- [x] Hostname-based detection
- [x] Fallback mechanisms

## 🎯 **ADVANTAGES OF THIS APPROACH**

1. **No Environment Variables**: No need to set `REACT_APP_API_URL`
2. **Automatic Detection**: Works in all environments automatically
3. **Professional**: Industry-standard environment detection
4. **Debugging**: Console logs show exactly what's happening
5. **Maintainable**: Clear logic, easy to understand
6. **Reliable**: No dependency on external configuration

---

**Last Updated:** 2025-07-24
**Status:** ✅ Implemented
**Approach:** Professional environment detection
**Benefits:** No environment variables, automatic configuration 