# Kafka Dashboard - PolicyBazaar

A comprehensive web-based dashboard for managing and monitoring Apache Kafka clusters. Built with React.js frontend and Node.js backend, this dashboard provides an intuitive interface for Kafka administration tasks with advanced Role-Based Access Control (RBAC).

## 🚀 Features

### ✅ Core Functionality
- **Topic Management**: View and browse Kafka topics
- **Consumer Group Monitoring**: View and monitor consumer groups with detailed member information
- **Message Browser**: Search and view messages across topics and partitions
- **Message Producer**: Send messages to Kafka topics with custom headers (1000 messages/day limit per user)
- **Real-time Monitoring**: Live message streaming via WebSocket
- **Cluster Information**: Monitor broker health and cluster status
- **Advanced User Management**: Complete RBAC system with role-based permissions
- **Dynamic Environment Switching**: Switch between QA and Production environments without restart

### 🎯 Key Capabilities
- **Dashboard Overview**: Real-time metrics and cluster health monitoring
- **Topic Operations**: 
  - View topic details including partition distribution
  - Browse messages by topic, partition, and offset
  - Real-time message streaming
  - Export messages to JSON
- **Message Operations**:
  - Browse messages by topic, partition, and offset
  - Real-time message streaming
  - Export messages to JSON
  - Send custom messages with headers (daily limit enforced)
- **Consumer Group Management**:
  - View all consumer groups with their states and member counts
  - Monitor group members and their assignments with detailed information
  - View offset information per topic/partition
  - Role-based access to consumer groups based on topic permissions
- **Monitoring & Analytics**:
  - Cluster health status
  - Broker information and connectivity
  - Message flow visualization
  - Real-time updates via WebSocket
- **Advanced User Management with RBAC**:
  - **Super Admin**: Full access to all features and user management
  - **Topic Manager**: Can view topics and assigned consumer groups
  - **Topic Viewer**: Read-only access to assigned topics and their consumer groups
  - User activation/deactivation
  - Topic assignment management
  - Professional mobile-responsive design
- **Environment Management**:
  - Dynamic switching between QA and Production
  - No restart required for environment changes
  - Environment-specific data isolation

## 🏗️ Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Frontend (React.js)                     │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐    │
│  │  Dashboard  │ │   Topics    │ │  Consumer Groups   │    │
│  └─────────────┘ └─────────────┘ └─────────────────────┘    │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐    │
│  │  Producer   │ │   Browser   │ │    Cluster Info    │    │
│  └─────────────┘ └─────────────┘ └─────────────────────┘    │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐    │
│  │User Mgmt.   │ │  Settings   │ │   Environment      │    │
│  └─────────────┘ └─────────────┘ └─────────────────────┘    │
└─────────────────────────────────────────────────────────────┘
                              │
                         HTTP/WebSocket
                              │
┌─────────────────────────────────────────────────────────────┐
│                    Backend (Node.js)                       │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐    │
│  │ Express.js  │ │  Socket.IO  │ │    Kafka Client     │    │
│  │   Server    │ │   Server    │ │     (kafkajs)       │    │
│  └─────────────┘ └─────────────┘ └─────────────────────┘    │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐    │
│  │   RBAC      │ │  MongoDB    │ │   Environment       │    │
│  │   Service   │ │   Models    │ │    Service          │    │
│  └─────────────┘ └─────────────┘ └─────────────────────┘    │
└─────────────────────────────────────────────────────────────┘
                              │
                         Kafka Protocol
                              │
┌─────────────────────────────────────────────────────────────┐
│                    Kafka Cluster                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐    │
│  │   Broker 1  │ │   Broker 2  │ │      Broker 3      │    │
│  └─────────────┘ └─────────────┘ └─────────────────────┘    │
└─────────────────────────────────────────────────────────────┘
```

## 📋 Prerequisites

- **Node.js** (v16 or higher)
- **npm** or **yarn**
- **Apache Kafka** cluster (running and accessible)
- **MongoDB** (for user management and RBAC)

## 🚀 Quick Start

### 1. Setup
```bash
# Clone and setup
git clone <repository-url>
cd kafka-dashboard
./setup.sh
```

### 2. Start Application
```bash
# Start development servers
./deploy.sh start

# Or use npm directly
npm run dev
```

### 3. Access Application
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:5000
- **Health Check**: http://localhost:5000/health

### 4. Login
- **Username**: admin
- **Password**: admin@123

## 📁 Project Structure

```
kafka-dashboard/
├── backend/                 # Node.js backend server
│   ├── config/             # Configuration files
│   ├── routes/             # API routes
│   ├── middleware/         # Express middleware
│   ├── models/             # MongoDB models
│   ├── services/           # Business logic
│   └── utils/              # Utility functions
├── frontend/               # React.js frontend
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── pages/          # Page components
│   │   ├── services/       # API services
│   │   ├── contexts/       # React contexts
│   │   └── hooks/          # Custom hooks
│   └── public/             # Static assets
├── deploy.sh               # Deployment script
├── setup.sh                # Setup script
├── README.md               # This file
├── API_ENDPOINTS.md        # API documentation
├── RBAC_IMPLEMENTATION.md  # RBAC documentation
└── DEPLOYMENT_CONFIG.md    # Deployment guide
```

## 🎯 Key Features

- **View-only Topic Management** (VAPT compliant)
- **Message Production** with daily limits (1000/day per user)
- **Consumer Group Monitoring**
- **Real-time Message Streaming**
- **Role-Based Access Control**
- **Dynamic Environment Switching**
- **Professional UI/UX**

## 🔧 Available Scripts

```bash
# Development
npm run dev          # Start development servers
npm run build        # Build for production
npm run start        # Start production servers

# Deployment
./deploy.sh start    # Start application
./deploy.sh stop     # Stop application
./deploy.sh restart  # Restart application
./deploy.sh status   # Check status

# Setup
./setup.sh           # Initial setup
```

## 📚 Documentation

- **API_ENDPOINTS.md**: Complete API documentation
- **RBAC_IMPLEMENTATION.md**: Role-based access control details
- **DEPLOYMENT_CONFIG.md**: Deployment and configuration guide 