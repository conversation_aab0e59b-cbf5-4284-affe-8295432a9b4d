{"name": "kafka-dashboard", "version": "1.0.0", "description": "Comprehensive Kafka Dashboard for topic management, monitoring, and administration with dynamic environment switching", "main": "index.js", "scripts": {"dev": "concurrently \"npm run backend:dev\" \"npm run frontend:dev\"", "start": "concurrently \"npm run backend:start\" \"npm run frontend:start\"", "build": "npm run frontend:build", "install": "npm install && cd backend && npm install && cd ../frontend && npm install", "backend:dev": "cd backend && npm run dev", "backend:start": "cd backend && npm start", "frontend:dev": "cd frontend && npm start", "frontend:start": "cd frontend && npm start", "frontend:build": "cd frontend && npm run build", "test": "npm run backend:test && npm run frontend:test", "backend:test": "cd backend && npm test", "frontend:test": "cd frontend && npm test -- --watchAll=false", "clean": "rm -rf node_modules backend/node_modules frontend/node_modules", "clean:install": "npm run clean && npm run install"}, "keywords": ["kafka", "dashboard", "react", "nodejs", "monitoring", "environment-switching"], "author": "PolicyBazaar", "license": "MIT", "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "devDependencies": {"concurrently": "^8.2.2"}, "dependencies": {"axios": "1.10.0", "bcryptjs": "3.0.2"}}