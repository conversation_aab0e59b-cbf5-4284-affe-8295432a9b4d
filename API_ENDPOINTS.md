# Kafka Dashboard API Endpoints Documentation

## Base URL
- **Development**: `http://localhost:5000/api`
- **Production**: `https://your-domain.com/api`

## Authentication
All endpoints except `/auth/login` require a valid JWT token in the Authorization header:
```
Authorization: Bearer <jwt_token>
```

---

## Authentication Endpoints

### POST `/auth/login`
**Description**: User login
**Authentication**: Not required
**Rate Limit**: 5 requests per minute per IP
**Request Body**:
```json
{
  "username": "string",
  "password": "string"
}
```
**Response**:
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "user": {
      "_id": "string",
      "username": "string",
      "email": "string",
      "role": "string",
      "assignedTopics": ["string"],
      "hasAllTopicsAccess": boolean
    },
    "token": "string",
    "permissions": {
      "role": "string",
      "permissions": ["string"],
      "assignedTopics": ["string"],
      "hasAllTopicsAccess": boolean
    }
  }
}
```

### GET `/auth/permissions`
**Description**: Get current user's permissions
**Authentication**: Required
**Response**:
```json
{
  "success": true,
  "data": {
    "role": "string",
    "permissions": ["string"],
    "assignedTopics": ["string"],
    "hasAllTopicsAccess": boolean
  }
}
```

### GET `/auth/profile`
**Description**: Get current user's profile
**Authentication**: Required
**Response**:
```json
{
  "success": true,
  "data": {
    "_id": "string",
    "username": "string",
    "email": "string",
    "role": "string",
    "assignedTopics": ["string"],
    "hasAllTopicsAccess": boolean,
    "isActive": boolean,
    "createdAt": "string",
    "updatedAt": "string"
  }
}
```

### GET `/auth/users`
**Description**: Get all users (Super Admin only)
**Authentication**: Required
**Permissions**: SUPER_ADMIN
**Rate Limit**: 30 requests per minute per IP
**Response**:
```json
{
  "success": true,
  "data": [
    {
      "_id": "string",
      "username": "string",
      "email": "string",
      "role": "string",
      "assignedTopics": ["string"],
      "hasAllTopicsAccess": boolean,
      "isActive": boolean
    }
  ]
}
```

### POST `/auth/users`
**Description**: Create new user (Super Admin only)
**Authentication**: Required
**Permissions**: SUPER_ADMIN
**Rate Limit**: 10 requests per minute per IP
**Request Body**:
```json
{
  "username": "string",
  "email": "string",
  "password": "string",
  "role": "string",
  "assignedTopics": ["string"],
  "hasAllTopicsAccess": boolean
}
```

### PUT `/auth/users/:userId`
**Description**: Update user (Super Admin only)
**Authentication**: Required
**Permissions**: SUPER_ADMIN
**Rate Limit**: 10 requests per minute per IP
**Request Body**:
```json
{
  "username": "string",
  "email": "string",
  "role": "string",
  "assignedTopics": ["string"],
  "hasAllTopicsAccess": boolean,
  "isActive": boolean
}
```

### DELETE `/auth/users/:userId`
**Description**: Delete user (Super Admin only)
**Authentication**: Required
**Permissions**: SUPER_ADMIN
**Rate Limit**: 5 requests per minute per IP

### POST `/auth/register`
**Description**: Register new user (Super Admin only)
**Authentication**: Required
**Permissions**: SUPER_ADMIN
**Rate Limit**: 5 requests per minute per IP
**Request Body**:
```json
{
  "username": "string",
  "email": "string",
  "role": "string",
  "assignedTopics": ["string"],
  "hasAllTopicsAccess": boolean
}
```

### POST `/auth/change-password`
**Description**: Change current user's password
**Authentication**: Required
**Rate Limit**: 5 requests per minute per IP
**Request Body**:
```json
{
  "currentPassword": "string",
  "newPassword": "string"
}
```

### POST `/auth/reset-password/:userId`
**Description**: Reset user password (Super Admin only)
**Authentication**: Required
**Permissions**: SUPER_ADMIN
**Rate Limit**: 5 requests per minute per IP
**Request Body**:
```json
{
  "newPassword": "string"
}
```

### POST `/auth/logout`
**Description**: Logout current user
**Authentication**: Required
**Rate Limit**: 10 requests per minute per IP

### GET `/auth/roles`
**Description**: Get available roles (Super Admin only)
**Authentication**: Required
**Permissions**: SUPER_ADMIN
**Rate Limit**: 30 requests per minute per IP

### PUT `/auth/users/:userId/role`
**Description**: Update user role (Super Admin only)
**Authentication**: Required
**Permissions**: SUPER_ADMIN
**Rate Limit**: 5 requests per minute per IP
**Request Body**:
```json
{
  "role": "string",
  "assignedTopics": ["string"],
  "hasAllTopicsAccess": boolean
}
```

---

## Topics Endpoints

### GET `/topics`
**Description**: Get all topics (filtered by user permissions)
**Authentication**: Required
**Rate Limit**: 30 requests per minute per IP
**Query Parameters**:
- `includeCounts` (optional): "true" to include message counts
**Response**:
```json
{
  "success": true,
  "data": [
    {
      "name": "string",
      "partitions": number,
      "partitionDetails": [
        {
          "partition": number,
          "leader": number,
          "replicas": [number],
          "isr": [number]
        }
      ]
    }
  ]
}
```

### GET `/topics/:topicName`
**Description**: Get specific topic details
**Authentication**: Required
**Permissions**: Topic access required
**Rate Limit**: 30 requests per minute per IP
**Response**:
```json
{
  "success": true,
  "data": {
    "name": "string",
    "partitions": number,
    "partitionDetails": [...],
    "config": {...}
  }
}
```

### POST `/topics`
**Description**: Create new topic (REMOVED FOR VAPT COMPLIANCE)
**Authentication**: Required
**Permissions**: SUPER_ADMIN
**Rate Limit**: 10 requests per minute per IP
**Request Body**:
```json
{
  "name": "string",
  "partitions": number,
  "replicationFactor": number,
  "configs": {}
}
```

### DELETE `/topics/:topicName`
**Description**: Delete topic (REMOVED FOR VAPT COMPLIANCE)
**Authentication**: Required
**Permissions**: SUPER_ADMIN
**Rate Limit**: 5 requests per minute per IP

### POST `/topics/:topicName/partitions`
**Description**: Add partitions to topic (REMOVED FOR VAPT COMPLIANCE)
**Authentication**: Required
**Permissions**: SUPER_ADMIN
**Rate Limit**: 10 requests per minute per IP
**Request Body**:
```json
{
  "partitionCount": number
}
```

### GET `/topics/:topicName/messages`
**Description**: Get messages from topic
**Authentication**: Required
**Permissions**: Topic access required
**Rate Limit**: 100 requests per minute per IP
**Query Parameters**:
- `partition` (optional): Partition number (-1 for all)
- `offset` (optional): Starting offset
- `limit` (optional): Number of messages (max 1000)
- `startFrom` (optional): "latest" or "earliest"
**Response**:
```json
{
  "success": true,
  "data": [
    {
      "topic": "string",
      "partition": number,
      "offset": number,
      "key": "string",
      "value": "string",
      "timestamp": number,
      "headers": {}
    }
  ],
  "metadata": {
    "topicName": "string",
    "partition": number,
    "limit": number,
    "startFrom": "string",
    "messageCount": number,
    "timestamp": "string"
  }
}
```

### POST `/topics/:topicName/messages`
**Description**: Produce message to topic (Daily limit: 1000 messages per user)
**Authentication**: Required
**Permissions**: Topic access + PRODUCE_MESSAGES
**Rate Limit**: 100 requests per minute per IP
**Daily Limit**: 1000 messages per user per day
**Request Body**:
```json
{
  "value": "string",
  "key": "string",
  "headers": {}
}
```
**Response Headers**:
- `X-Remaining-Messages`: Number of remaining messages for the day

### POST `/topics/:topicName/messages/bulk`
**Description**: Produce multiple messages to topic (Daily limit: 1000 messages per user)
**Authentication**: Required
**Permissions**: Topic access + PRODUCE_MESSAGES
**Rate Limit**: 100 requests per minute per IP
**Daily Limit**: 1000 messages per user per day
**Request Body**:
```json
{
  "messages": [
    {
      "value": "string",
      "key": "string",
      "headers": {}
    }
  ],
  "batchSize": number
}
```
**Response Headers**:
- `X-Remaining-Messages`: Number of remaining messages for the day

### GET `/topics/:topicName/search`
**Description**: Search messages in topic
**Authentication**: Required
**Permissions**: Topic access required
**Rate Limit**: 100 requests per minute per IP
**Query Parameters**:
- `partition` (optional): Partition number
- `limit` (optional): Number of messages (max 1000)
- `startFrom` (optional): "latest" or "earliest"
- `key` (optional): Search in message keys
- `value` (optional): Search in message values
- `startTimestamp` (optional): ISO 8601 timestamp
- `endTimestamp` (optional): ISO 8601 timestamp
- `caseSensitive` (optional): "true" or "false"

### POST `/topics/:topicName/subscribe`
**Description**: Subscribe to real-time messages
**Authentication**: Required
**Permissions**: Topic access required
**Rate Limit**: 10 requests per minute per IP

### POST `/topics/:topicName/unsubscribe`
**Description**: Unsubscribe from real-time messages
**Authentication**: Required
**Permissions**: Topic access required
**Rate Limit**: 10 requests per minute per IP

### GET `/topics/:topicName/config`
**Description**: Get topic configuration
**Authentication**: Required
**Permissions**: Topic access required
**Rate Limit**: 30 requests per minute per IP

### GET `/topics/:topicName/message-count`
**Description**: Get message count for topic
**Authentication**: Required
**Permissions**: Topic access required
**Rate Limit**: 30 requests per minute per IP

---

## Consumer Groups Endpoints

### GET `/consumers`
**Description**: Get all consumer groups (filtered by user permissions)
**Authentication**: Required
**Rate Limit**: 30 requests per minute per IP
**Response**:
```json
{
  "success": true,
  "data": [
    {
      "groupId": "string",
      "state": "string",
      "members": number,
      "topics": ["string"]
    }
  ]
}
```

### GET `/consumers/:groupId`
**Description**: Get consumer group details
**Authentication**: Required
**Permissions**: Consumer group access required
**Rate Limit**: 30 requests per minute per IP
**Response**:
```json
{
  "success": true,
  "data": {
    "groupId": "string",
    "state": "string",
    "members": [
      {
        "memberId": "string",
        "clientId": "string",
        "clientHost": "string",
        "assignments": [
          {
            "topic": "string",
            "partition": number
          }
        ]
      }
    ],
    "offsets": [
      {
        "topic": "string",
        "partition": number,
        "offset": number,
        "metadata": "string"
      }
    ]
  }
}
```

### DELETE `/consumers/:groupId`
**Description**: Delete consumer group (REMOVED FOR VAPT COMPLIANCE)
**Authentication**: Required
**Permissions**: Consumer group access + DELETE_CONSUMER_GROUPS
**Rate Limit**: 5 requests per minute per IP

---

## Cluster Endpoints

### GET `/cluster/info`
**Description**: Get cluster information and health
**Authentication**: Required
**Permissions**: All authenticated users
**Rate Limit**: 30 requests per minute per IP
**Response**:
```json
{
  "success": true,
  "data": {
    "brokers": [
      {
        "nodeId": number,
        "host": "string",
        "port": number,
        "rack": "string"
      }
    ],
    "topics": number,
    "partitions": number,
    "health": {
      "status": "string",
      "message": "string",
      "timestamp": "string"
    },
    "environment": {
      "key": "string",
      "name": "string"
    }
  }
}
```

---

## Environment Endpoints

### GET `/environment/current`
**Description**: Get current environment
**Authentication**: Required
**Rate Limit**: 30 requests per minute per IP
**Response**:
```json
{
  "success": true,
  "data": {
    "key": "string",
    "name": "string",
    "description": "string"
  }
}
```

### GET `/environment/environments`
**Description**: Get all available environments
**Authentication**: Required
**Rate Limit**: 30 requests per minute per IP
**Response**:
```json
{
  "success": true,
  "data": [
    {
      "key": "string",
      "name": "string",
      "description": "string",
      "brokerCount": number
    }
  ]
}
```

### POST `/environment/switch`
**Description**: Switch environment (Super Admin only)
**Authentication**: Required
**Permissions**: SUPER_ADMIN
**Rate Limit**: 5 requests per minute per IP
**Request Body**:
```json
{
  "environment": "string"
}
```

### POST `/environment/test-connection`
**Description**: Test connection to environment (Super Admin only)
**Authentication**: Required
**Permissions**: SUPER_ADMIN
**Rate Limit**: 10 requests per minute per IP
**Request Body**:
```json
{
  "environment": "string"
}
```

---

## Health Check Endpoint

### GET `/health`
**Description**: Health check endpoint
**Authentication**: Not required
**Rate Limit**: None
**Response**:
```json
{
  "status": "healthy",
  "timestamp": "string",
  "uptime": number,
  "kafka": "connected|disconnected"
}
```

---

## WebSocket Events

### Connection Events
- `connect`: Client connected
- `disconnect`: Client disconnected
- `error`: Connection error

### Topic Subscription Events
- `subscribe-topic`: Subscribe to topic messages
- `unsubscribe-topic`: Unsubscribe from topic messages
- `subscription-confirmed`: Subscription confirmed
- `unsubscription-confirmed`: Unsubscription confirmed

### Message Events
- `message`: Real-time message received
  ```json
  {
    "topic": "string",
    "partition": number,
    "offset": number,
    "key": "string",
    "value": "string",
    "timestamp": number,
    "headers": {},
    "receivedAt": "string"
  }
  ```

---

## Rate Limiting

The API implements rate limiting with the following limits:
- **General endpoints**: 100 requests per 15 minutes per IP
- **Authentication**: 5 requests per minute per IP
- **Topic operations**: 10 requests per minute per IP
- **Message operations**: 100 requests per minute per IP
- **User management**: 10 requests per minute per IP
- **Environment switching**: 5 requests per minute per IP

---

## Error Responses

All endpoints return consistent error responses:

```json
{
  "success": false,
  "error": {
    "message": "Error description",
    "status": 400
  }
}
```

Common HTTP status codes:
- `200`: Success
- `400`: Bad Request
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `429`: Too Many Requests
- `500`: Internal Server Error

---

## Security Considerations

1. **Authentication**: JWT tokens with 24-hour expiration
2. **Authorization**: Role-based access control (RBAC)
3. **Rate Limiting**: Per-endpoint rate limiting
4. **Input Validation**: All inputs are validated
5. **CORS**: Configured for specific origins
6. **Helmet**: Security headers enabled
7. **Compression**: Response compression enabled

---

## Environment Variables

Required environment variables:
- `PORT`: Server port (default: 5000)
- `JWT_SECRET`: JWT signing secret
- `MONGODB_URI`: MongoDB connection string
- `KAFKA_BROKERS`: Comma-separated Kafka broker addresses
- `KAFKA_CLIENT_ID`: Kafka client ID
- `CORS_ORIGIN`: Allowed CORS origin
- `LOG_LEVEL`: Logging level (default: info) 