#!/bin/bash

echo "🚀 Setting up Kafka Dashboard..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js (v16+) and try again."
    exit 1
fi

echo "✅ Node.js version: $(node -v)"

# Install dependencies
echo "📦 Installing dependencies..."
npm install

echo "📦 Installing backend dependencies..."
cd backend && npm install && cd ..

echo "📦 Installing frontend dependencies..."
cd frontend && npm install && cd ..

# Create logs directory
mkdir -p backend/logs

echo ""
echo "🎉 Setup complete!"
echo ""
echo "Next steps:"
echo "1. Run: ./deploy.sh start"
echo "2. Open: http://localhost:3000"
echo "3. Login with default admin credentials:"
echo "   Username: admin"
echo "   Password: admin@123"